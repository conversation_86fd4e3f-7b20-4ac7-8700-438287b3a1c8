self.__REACT_LOADABLE_MANIFEST="{\"node_modules\\\\@sanity\\\\client\\\\dist\\\\index.browser.js -> ./_chunks-es/stegaEncodeSourceMap.js\":{\"id\":\"node_modules\\\\@sanity\\\\client\\\\dist\\\\index.browser.js -> ./_chunks-es/stegaEncodeSourceMap.js\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_sanity_client_dist__chunks-es_stegaEncodeSourceMap_js.js\"]},\"node_modules\\\\@sanity\\\\client\\\\dist\\\\index.browser.js -> @sanity/eventsource\":{\"id\":\"node_modules\\\\@sanity\\\\client\\\\dist\\\\index.browser.js -> @sanity/eventsource\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_sanity_eventsource_browser_js.js\"]},\"node_modules\\\\@sanity\\\\ui\\\\dist\\\\_chunks-es\\\\_visual-editing.mjs -> ./refractor.mjs\":{\"id\":\"node_modules\\\\@sanity\\\\ui\\\\dist\\\\_chunks-es\\\\_visual-editing.mjs -> ./refractor.mjs\",\"files\":[]},\"node_modules\\\\next-sanity\\\\dist\\\\visual-editing\\\\client-component.js -> ../_chunks-es/VisualEditing.js\":{\"id\":\"node_modules\\\\next-sanity\\\\dist\\\\visual-editing\\\\client-component.js -> ../_chunks-es/VisualEditing.js\",\"files\":[]},\"node_modules\\\\next\\\\dist\\\\client\\\\components\\\\react-dev-overlay\\\\utils\\\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts\":{\"id\":\"node_modules\\\\next\\\\dist\\\\client\\\\components\\\\react-dev-overlay\\\\utils\\\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js\"]},\"src\\\\components\\\\RecentlyAddedProperties.tsx -> @/lib/api\":{\"id\":\"src\\\\components\\\\RecentlyAddedProperties.tsx -> @/lib/api\",\"files\":[\"static/chunks/_app-pages-browser_src_lib_api_ts.js\"]}}"