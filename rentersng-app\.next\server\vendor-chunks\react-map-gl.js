"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-map-gl";
exports.ids = ["vendor-chunks/react-map-gl"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/attribution-control.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/attribution-control.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AttributionControl: () => (/* binding */ AttributionControl)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/apply-react-style.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/apply-react-style.js\");\n/* harmony import */ var _use_control_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-control.js\");\n\n\n\nfunction _AttributionControl(props) {\n    const ctrl = (0,_use_control_js__WEBPACK_IMPORTED_MODULE_2__.useControl)({\n        \"_AttributionControl.useControl[ctrl]\": ({ mapLib })=>new mapLib.AttributionControl(props)\n    }[\"_AttributionControl.useControl[ctrl]\"], {\n        position: props.position\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"_AttributionControl.useEffect\": ()=>{\n            // @ts-expect-error accessing private member\n            (0,_utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_1__.applyReactStyle)(ctrl._container, props.style);\n        }\n    }[\"_AttributionControl.useEffect\"], [\n        props.style\n    ]);\n    return null;\n}\nconst AttributionControl = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(_AttributionControl); //# sourceMappingURL=attribution-control.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS9jb21wb25lbnRzL2F0dHJpYnV0aW9uLWNvbnRyb2wuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUNzQztBQUNxQjtBQUNsQjtBQVd6QyxTQUFTLG1CQUFtQixDQUFDLEtBQThCO0lBQ3pELE1BQU0sSUFBSSxHQUFHLDJEQUFVO2dEQUFDLENBQUMsRUFBQyxNQUFNLEVBQUMsRUFBRSxDQUFHLENBQUQsR0FBSyxNQUFNLENBQUMsa0JBQWtCLENBQUMsS0FBSyxDQUFDOytDQUFFO1FBQzFFLFFBQVEsRUFBRSxLQUFLLENBQUMsUUFBUTtLQUN6QixDQUFDLENBQUM7SUFFSCxnREFBUzt5Q0FBQyxHQUFHLEVBQUU7WUFDYiw0Q0FBNEM7WUFDNUMsNEVBQWUsQ0FBQyxJQUFJLENBQUMsVUFBVSxFQUFFLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUNoRCxDQUFDO3dDQUFFO1FBQUMsS0FBSyxDQUFDLEtBQUs7S0FBQyxDQUFDLENBQUM7SUFFbEIsT0FBTyxJQUFJLENBQUM7QUFDZCxDQUFDO0FBRU0sTUFBTSxrQkFBa0IsaUJBQUcsMkNBQUksQ0FBQyxtQkFBbUIsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERvY3VtZW50c1xcc3JjXFxtYXBib3gtbGVnYWN5XFxjb21wb25lbnRzXFxhdHRyaWJ1dGlvbi1jb250cm9sLnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/attribution-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/fullscreen-control.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/fullscreen-control.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FullscreenControl: () => (/* binding */ FullscreenControl)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/apply-react-style.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/apply-react-style.js\");\n/* harmony import */ var _use_control_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-control.js\");\n\n\n\nfunction _FullscreenControl(props) {\n    const ctrl = (0,_use_control_js__WEBPACK_IMPORTED_MODULE_2__.useControl)({\n        \"_FullscreenControl.useControl[ctrl]\": ({ mapLib })=>new mapLib.FullscreenControl({\n                container: props.containerId && document.getElementById(props.containerId)\n            })\n    }[\"_FullscreenControl.useControl[ctrl]\"], {\n        position: props.position\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"_FullscreenControl.useEffect\": ()=>{\n            // @ts-expect-error accessing private member\n            (0,_utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_1__.applyReactStyle)(ctrl._controlContainer, props.style);\n        }\n    }[\"_FullscreenControl.useEffect\"], [\n        props.style\n    ]);\n    return null;\n}\nconst FullscreenControl = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(_FullscreenControl); //# sourceMappingURL=fullscreen-control.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS9jb21wb25lbnRzL2Z1bGxzY3JlZW4tY29udHJvbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBRXNDO0FBQ3FCO0FBQ2xCO0FBY3pDLFNBQVMsa0JBQWtCLENBQUMsS0FBNkI7SUFDdkQsTUFBTSxJQUFJLEdBQUcsMkRBQVU7K0NBQ3JCLENBQUMsRUFBQyxNQUFNLEVBQUMsRUFBRSxDQUNULENBRFcsR0FDUCxNQUFNLENBQUMsaUJBQWlCLENBQUM7Z0JBQzNCLFNBQVMsRUFBRSxLQUFLLENBQUMsV0FBVyxJQUFJLFFBQVEsQ0FBQyxjQUFjLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FBQzthQUMzRSxDQUFDOzhDQUNKO1FBQUMsUUFBUSxFQUFFLEtBQUssQ0FBQyxRQUFRO0lBQUEsQ0FBQyxDQUMzQixDQUFDO0lBRUYsZ0RBQVM7d0NBQUMsR0FBRyxFQUFFO1lBQ2IsNENBQTRDO1lBQzVDLDRFQUFlLENBQUMsSUFBSSxDQUFDLGlCQUFpQixFQUFFLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQztRQUN2RCxDQUFDO3VDQUFFO1FBQUMsS0FBSyxDQUFDLEtBQUs7S0FBQyxDQUFDLENBQUM7SUFFbEIsT0FBTyxJQUFJLENBQUM7QUFDZCxDQUFDO0FBRU0sTUFBTSxpQkFBaUIsaUJBQUcsMkNBQUksQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVTRVJcXERvY3VtZW50c1xcc3JjXFxtYXBib3gtbGVnYWN5XFxjb21wb25lbnRzXFxmdWxsc2NyZWVuLWNvbnRyb2wudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/fullscreen-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/geolocate-control.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/geolocate-control.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GeolocateControl: () => (/* binding */ GeolocateControl)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/apply-react-style.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/apply-react-style.js\");\n/* harmony import */ var _use_control_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-control.js\");\n\n\n\nfunction _GeolocateControl(props, ref) {\n    const thisRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        props\n    });\n    const ctrl = (0,_use_control_js__WEBPACK_IMPORTED_MODULE_2__.useControl)({\n        \"_GeolocateControl.useControl[ctrl]\": ({ mapLib })=>{\n            const gc = new mapLib.GeolocateControl(props);\n            // Hack: fix GeolocateControl reuse\n            // When using React strict mode, the component is mounted twice.\n            // GeolocateControl's UI creation is asynchronous. Removing and adding it back causes the UI to be initialized twice.\n            // @ts-expect-error accessing private method\n            const setupUI = gc._setupUI.bind(gc);\n            // @ts-expect-error overriding private method\n            gc._setupUI = ({\n                \"_GeolocateControl.useControl[ctrl]\": (args)=>{\n                    // @ts-expect-error accessing private member\n                    if (!gc._container.hasChildNodes()) {\n                        setupUI(args);\n                    }\n                }\n            })[\"_GeolocateControl.useControl[ctrl]\"];\n            gc.on('geolocate', {\n                \"_GeolocateControl.useControl[ctrl]\": (e)=>{\n                    thisRef.current.props.onGeolocate?.(e);\n                }\n            }[\"_GeolocateControl.useControl[ctrl]\"]);\n            gc.on('error', {\n                \"_GeolocateControl.useControl[ctrl]\": (e)=>{\n                    thisRef.current.props.onError?.(e);\n                }\n            }[\"_GeolocateControl.useControl[ctrl]\"]);\n            gc.on('outofmaxbounds', {\n                \"_GeolocateControl.useControl[ctrl]\": (e)=>{\n                    thisRef.current.props.onOutOfMaxBounds?.(e);\n                }\n            }[\"_GeolocateControl.useControl[ctrl]\"]);\n            gc.on('trackuserlocationstart', {\n                \"_GeolocateControl.useControl[ctrl]\": (e)=>{\n                    thisRef.current.props.onTrackUserLocationStart?.(e);\n                }\n            }[\"_GeolocateControl.useControl[ctrl]\"]);\n            gc.on('trackuserlocationend', {\n                \"_GeolocateControl.useControl[ctrl]\": (e)=>{\n                    thisRef.current.props.onTrackUserLocationEnd?.(e);\n                }\n            }[\"_GeolocateControl.useControl[ctrl]\"]);\n            return gc;\n        }\n    }[\"_GeolocateControl.useControl[ctrl]\"], {\n        position: props.position\n    });\n    thisRef.current.props = props;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, {\n        \"_GeolocateControl.useImperativeHandle\": ()=>ctrl\n    }[\"_GeolocateControl.useImperativeHandle\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"_GeolocateControl.useEffect\": ()=>{\n            // @ts-expect-error accessing private member\n            (0,_utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_1__.applyReactStyle)(ctrl._container, props.style);\n        }\n    }[\"_GeolocateControl.useEffect\"], [\n        props.style\n    ]);\n    return null;\n}\nconst GeolocateControl = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_GeolocateControl)); //# sourceMappingURL=geolocate-control.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/geolocate-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/layer.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/layer.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Layer: () => (/* binding */ Layer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/map.js\");\n/* harmony import */ var _utils_assert_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/assert.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/assert.js\");\n/* harmony import */ var _utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/deep-equal.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/deep-equal.js\");\n\n\n\n\n/* eslint-disable complexity, max-statements */ function updateLayer(map, id, props, prevProps) {\n    (0,_utils_assert_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props.id === prevProps.id, 'layer id changed');\n    (0,_utils_assert_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props.type === prevProps.type, 'layer type changed');\n    if (props.type === 'custom' || prevProps.type === 'custom') {\n        return;\n    }\n    // @ts-ignore filter does not exist in some Layer types\n    const { layout = {}, paint = {}, filter, minzoom, maxzoom, beforeId } = props;\n    if (beforeId !== prevProps.beforeId) {\n        map.moveLayer(id, beforeId);\n    }\n    if (layout !== prevProps.layout) {\n        const prevLayout = prevProps.layout || {};\n        for(const key in layout){\n            if (!(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_3__.deepEqual)(layout[key], prevLayout[key])) {\n                map.setLayoutProperty(id, key, layout[key]);\n            }\n        }\n        for(const key in prevLayout){\n            if (!layout.hasOwnProperty(key)) {\n                map.setLayoutProperty(id, key, undefined);\n            }\n        }\n    }\n    if (paint !== prevProps.paint) {\n        const prevPaint = prevProps.paint || {};\n        for(const key in paint){\n            if (!(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_3__.deepEqual)(paint[key], prevPaint[key])) {\n                map.setPaintProperty(id, key, paint[key]);\n            }\n        }\n        for(const key in prevPaint){\n            if (!paint.hasOwnProperty(key)) {\n                map.setPaintProperty(id, key, undefined);\n            }\n        }\n    }\n    // @ts-ignore filter does not exist in some Layer types\n    if (!(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_3__.deepEqual)(filter, prevProps.filter)) {\n        map.setFilter(id, filter);\n    }\n    if (minzoom !== prevProps.minzoom || maxzoom !== prevProps.maxzoom) {\n        map.setLayerZoomRange(id, minzoom, maxzoom);\n    }\n}\nfunction createLayer(map, id, props) {\n    // @ts-ignore\n    if (map.style && map.style._loaded && (!('source' in props) || map.getSource(props.source))) {\n        const options = {\n            ...props,\n            id\n        };\n        delete options.beforeId;\n        // @ts-ignore\n        map.addLayer(options, props.beforeId);\n    }\n}\n/* eslint-enable complexity, max-statements */ let layerCounter = 0;\nfunction Layer(props) {\n    const map = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_map_js__WEBPACK_IMPORTED_MODULE_1__.MapContext).map.getMap();\n    const propsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props);\n    const [, setStyleLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"Layer.useMemo[id]\": ()=>props.id || `jsx-layer-${layerCounter++}`\n    }[\"Layer.useMemo[id]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Layer.useEffect\": ()=>{\n            if (map) {\n                const forceUpdate = {\n                    \"Layer.useEffect.forceUpdate\": ()=>setStyleLoaded({\n                            \"Layer.useEffect.forceUpdate\": (version)=>version + 1\n                        }[\"Layer.useEffect.forceUpdate\"])\n                }[\"Layer.useEffect.forceUpdate\"];\n                map.on('styledata', forceUpdate);\n                forceUpdate();\n                return ({\n                    \"Layer.useEffect\": ()=>{\n                        map.off('styledata', forceUpdate);\n                        // @ts-ignore\n                        if (map.style && map.style._loaded && map.getLayer(id)) {\n                            map.removeLayer(id);\n                        }\n                    }\n                })[\"Layer.useEffect\"];\n            }\n            return undefined;\n        }\n    }[\"Layer.useEffect\"], [\n        map\n    ]);\n    // @ts-ignore\n    const layer = map && map.style && map.getLayer(id);\n    if (layer) {\n        try {\n            updateLayer(map, id, props, propsRef.current);\n        } catch (error) {\n            console.warn(error); // eslint-disable-line\n        }\n    } else {\n        createLayer(map, id, props);\n    }\n    // Store last rendered props\n    propsRef.current = props;\n    return null;\n} //# sourceMappingURL=layer.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS9jb21wb25lbnRzL2xheWVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQXVFO0FBQ3RDO0FBQ0k7QUFDUztBQWU5QywrQ0FBK0MsQ0FDL0MsU0FBUyxXQUFXLENBQUMsR0FBZ0IsRUFBRSxFQUFVLEVBQUUsS0FBaUIsRUFBRSxTQUFxQjtJQUN6Riw0REFBTSxDQUFDLEtBQUssQ0FBQyxFQUFFLEtBQUssU0FBUyxDQUFDLEVBQUUsRUFBRSxrQkFBa0IsQ0FBQyxDQUFDO0lBQ3RELDREQUFNLENBQUMsS0FBSyxDQUFDLElBQUksS0FBSyxTQUFTLENBQUMsSUFBSSxFQUFFLG9CQUFvQixDQUFDLENBQUM7SUFFNUQsSUFBSSxLQUFLLENBQUMsSUFBSSxLQUFLLFFBQVEsSUFBSSxTQUFTLENBQUMsSUFBSSxLQUFLLFFBQVEsRUFBRSxDQUFDO1FBQzNELE9BQU87SUFDVCxDQUFDO0lBRUQsdURBQXVEO0lBQ3ZELE1BQU0sRUFBQyxNQUFNLEdBQUcsRUFBRSxFQUFFLEtBQUssR0FBRyxFQUFFLEVBQUUsTUFBTSxFQUFFLE9BQU8sRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFDLEdBQUcsS0FBSyxDQUFDO0lBRTVFLElBQUksUUFBUSxLQUFLLFNBQVMsQ0FBQyxRQUFRLEVBQUUsQ0FBQztRQUNwQyxHQUFHLENBQUMsU0FBUyxDQUFDLEVBQUUsRUFBRSxRQUFRLENBQUMsQ0FBQztJQUM5QixDQUFDO0lBQ0QsSUFBSSxNQUFNLEtBQUssU0FBUyxDQUFDLE1BQU0sRUFBRSxDQUFDO1FBQ2hDLE1BQU0sVUFBVSxHQUFHLFNBQVMsQ0FBQyxNQUFNLElBQUksRUFBRSxDQUFDO1FBQzFDLElBQUssTUFBTSxHQUFHLElBQUksTUFBTSxDQUFFLENBQUM7WUFDekIsSUFBSSxDQUFDLCtEQUFTLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxFQUFFLFVBQVUsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFLENBQUM7Z0JBQzdDLEdBQUcsQ0FBQyxpQkFBaUIsQ0FBQyxFQUFFLEVBQUUsR0FBVSxFQUFFLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO1lBQ3JELENBQUM7UUFDSCxDQUFDO1FBQ0QsSUFBSyxNQUFNLEdBQUcsSUFBSSxVQUFVLENBQUUsQ0FBQztZQUM3QixJQUFJLENBQUMsTUFBTSxDQUFDLGNBQWMsQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDO2dCQUNoQyxHQUFHLENBQUMsaUJBQWlCLENBQUMsRUFBRSxFQUFFLEdBQVUsRUFBRSxTQUFTLENBQUMsQ0FBQztZQUNuRCxDQUFDO1FBQ0gsQ0FBQztJQUNILENBQUM7SUFDRCxJQUFJLEtBQUssS0FBSyxTQUFTLENBQUMsS0FBSyxFQUFFLENBQUM7UUFDOUIsTUFBTSxTQUFTLEdBQUcsU0FBUyxDQUFDLEtBQUssSUFBSSxFQUFFLENBQUM7UUFDeEMsSUFBSyxNQUFNLEdBQUcsSUFBSSxLQUFLLENBQUUsQ0FBQztZQUN4QixJQUFJLENBQUMsK0RBQVMsQ0FBQyxLQUFLLENBQUMsR0FBRyxDQUFDLEVBQUUsU0FBUyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUUsQ0FBQztnQkFDM0MsR0FBRyxDQUFDLGdCQUFnQixDQUFDLEVBQUUsRUFBRSxHQUFVLEVBQUUsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUM7WUFDbkQsQ0FBQztRQUNILENBQUM7UUFDRCxJQUFLLE1BQU0sR0FBRyxJQUFJLFNBQVMsQ0FBRSxDQUFDO1lBQzVCLElBQUksQ0FBQyxLQUFLLENBQUMsY0FBYyxDQUFDLEdBQUcsQ0FBQyxFQUFFLENBQUM7Z0JBQy9CLEdBQUcsQ0FBQyxnQkFBZ0IsQ0FBQyxFQUFFLEVBQUUsR0FBVSxFQUFFLFNBQVMsQ0FBQyxDQUFDO1lBQ2xELENBQUM7UUFDSCxDQUFDO0lBQ0gsQ0FBQztJQUVELHVEQUF1RDtJQUN2RCxJQUFJLENBQUMsK0RBQVMsQ0FBQyxNQUFNLEVBQUUsU0FBUyxDQUFDLE1BQU0sQ0FBQyxFQUFFLENBQUM7UUFDekMsR0FBRyxDQUFDLFNBQVMsQ0FBQyxFQUFFLEVBQUUsTUFBTSxDQUFDLENBQUM7SUFDNUIsQ0FBQztJQUNELElBQUksT0FBTyxLQUFLLFNBQVMsQ0FBQyxPQUFPLElBQUksT0FBTyxLQUFLLFNBQVMsQ0FBQyxPQUFPLEVBQUUsQ0FBQztRQUNuRSxHQUFHLENBQUMsaUJBQWlCLENBQUMsRUFBRSxFQUFFLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQztJQUM5QyxDQUFDO0FBQ0gsQ0FBQztBQUVELFNBQVMsV0FBVyxDQUFDLEdBQWdCLEVBQUUsRUFBVSxFQUFFLEtBQWlCO0lBQ2xFLGFBQWE7SUFDYixJQUFJLEdBQUcsQ0FBQyxLQUFLLElBQUksR0FBRyxDQUFDLEtBQUssQ0FBQyxPQUFPLElBQUksQ0FBQyxDQUFDLENBQUMsUUFBUSxJQUFJLE1BQUssQ0FBQyxHQUFJLEdBQUcsQ0FBQyxTQUFTLENBQUMsS0FBSyxDQUFDLE9BQU0sQ0FBQyxDQUFDLENBQUUsQ0FBQztRQUM1RixNQUFNLE9BQU8sR0FBZTtZQUFDLEdBQUcsS0FBSztZQUFFLEVBQUU7UUFBQSxDQUFDLENBQUM7UUFDM0MsT0FBTyxPQUFPLENBQUMsUUFBUSxDQUFDO1FBRXhCLGFBQWE7UUFDYixHQUFHLENBQUMsUUFBUSxDQUFDLE9BQU8sRUFBRSxLQUFLLENBQUMsUUFBUSxDQUFDLENBQUM7SUFDeEMsQ0FBQztBQUNILENBQUM7QUFFRCw4Q0FBOEMsQ0FFOUMsSUFBSSxZQUFZLEdBQUcsQ0FBQyxDQUFDO0FBRWYsU0FBVSxLQUFLLENBQUMsS0FBaUI7SUFDckMsTUFBTSxHQUFHLEdBQUcsaURBQVUsQ0FBQywrQ0FBVSxDQUFDLENBQUMsR0FBRyxDQUFDLE1BQU0sRUFBRSxDQUFDO0lBQ2hELE1BQU0sUUFBUSxHQUFHLDZDQUFNLENBQUMsS0FBSyxDQUFDLENBQUM7SUFDL0IsTUFBTSxDQUFDLEVBQUUsY0FBYyxDQUFDLEdBQUcsK0NBQVEsQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUV2QyxNQUFNLEVBQUUsR0FBRyw4Q0FBTzs2QkFBQyxHQUFHLENBQUcsQ0FBRCxJQUFNLENBQUMsRUFBRSxJQUFJLGFBQWEsWUFBWSxFQUFFLEVBQUU7NEJBQUUsRUFBRSxDQUFDLENBQUM7SUFFeEUsZ0RBQVM7MkJBQUMsR0FBRyxFQUFFO1lBQ2IsSUFBSSxHQUFHLEVBQUUsQ0FBQztnQkFDUixNQUFNLFdBQVc7bURBQUcsR0FBRyxDQUFHLENBQUQsYUFBZTs0REFBQyxPQUFPLENBQUMsRUFBRSxPQUFRLEdBQUcsQ0FBQzs7O2dCQUMvRCxHQUFHLENBQUMsRUFBRSxDQUFDLFdBQVcsRUFBRSxXQUFXLENBQUMsQ0FBQztnQkFDakMsV0FBVyxFQUFFLENBQUM7Z0JBRWQ7dUNBQU8sR0FBRyxFQUFFO3dCQUNWLEdBQUcsQ0FBQyxHQUFHLENBQUMsV0FBVyxFQUFFLFdBQVcsQ0FBQyxDQUFDO3dCQUNsQyxhQUFhO3dCQUNiLElBQUksR0FBRyxDQUFDLEtBQUssSUFBSSxHQUFHLENBQUMsS0FBSyxDQUFDLE9BQU8sSUFBSSxHQUFHLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxFQUFFLENBQUM7NEJBQ3ZELEdBQUcsQ0FBQyxXQUFXLENBQUMsRUFBRSxDQUFDLENBQUM7d0JBQ3RCLENBQUM7b0JBQ0gsQ0FBQzs7WUFDSCxDQUFDO1lBQ0QsT0FBTyxTQUFTLENBQUM7UUFDbkIsQ0FBQzswQkFBRTtRQUFDLEdBQUc7S0FBQyxDQUFDLENBQUM7SUFFVixhQUFhO0lBQ2IsTUFBTSxLQUFLLEdBQUcsR0FBRyxJQUFJLEdBQUcsQ0FBQyxLQUFLLElBQUksR0FBRyxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQztJQUNuRCxJQUFJLEtBQUssRUFBRSxDQUFDO1FBQ1YsSUFBSSxDQUFDO1lBQ0gsV0FBVyxDQUFDLEdBQUcsRUFBRSxFQUFFLEVBQUUsS0FBSyxFQUFFLFFBQVEsQ0FBQyxPQUFPLENBQUMsQ0FBQztRQUNoRCxDQUFDLENBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztZQUNmLE9BQU8sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxzQkFBc0I7UUFDN0MsQ0FBQztJQUNILENBQUMsTUFBTSxDQUFDO1FBQ04sV0FBVyxDQUFDLEdBQUcsRUFBRSxFQUFFLEVBQUUsS0FBSyxDQUFDLENBQUM7SUFDOUIsQ0FBQztJQUVELDRCQUE0QjtJQUM1QixRQUFRLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztJQUV6QixPQUFPLElBQUksQ0FBQztBQUNkLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxzcmNcXG1hcGJveC1sZWdhY3lcXGNvbXBvbmVudHNcXGxheWVyLnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/layer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/map.js":
/*!************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/map.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Map: () => (/* binding */ Map),\n/* harmony export */   MapContext: () => (/* binding */ MapContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _use_map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-map.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-map.js\");\n/* harmony import */ var _mapbox_mapbox_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../mapbox/mapbox.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/mapbox/mapbox.js\");\n/* harmony import */ var _mapbox_create_ref_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../mapbox/create-ref.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/mapbox/create-ref.js\");\n/* harmony import */ var _utils_use_isomorphic_layout_effect_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/use-isomorphic-layout-effect.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/use-isomorphic-layout-effect.js\");\n/* harmony import */ var _utils_set_globals_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/set-globals.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/set-globals.js\");\n\n\n\n\n\n\n\nconst MapContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nfunction _Map(props, ref) {\n    const mountedMapsContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_use_map_js__WEBPACK_IMPORTED_MODULE_1__.MountedMapsContext);\n    const [mapInstance, setMapInstance] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const { current: contextValue } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        mapLib: null,\n        map: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"_Map.useEffect\": ()=>{\n            const mapLib = props.mapLib;\n            let isMounted = true;\n            let mapbox;\n            Promise.resolve(mapLib || __webpack_require__.e(/*! import() */ \"vendor-chunks/mapbox-gl\").then(__webpack_require__.t.bind(__webpack_require__, /*! mapbox-gl */ \"(ssr)/./node_modules/mapbox-gl/dist/mapbox-gl.js\", 19))).then({\n                \"_Map.useEffect\": (module)=>{\n                    if (!isMounted) {\n                        return;\n                    }\n                    if (!module) {\n                        throw new Error('Invalid mapLib');\n                    }\n                    const mapboxgl = 'Map' in module ? module : module.default;\n                    if (!mapboxgl.Map) {\n                        throw new Error('Invalid mapLib');\n                    }\n                    (0,_utils_set_globals_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(mapboxgl, props);\n                    if (props.reuseMaps) {\n                        mapbox = _mapbox_mapbox_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"].reuse(props, containerRef.current);\n                    }\n                    if (!mapbox) {\n                        mapbox = new _mapbox_mapbox_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"](mapboxgl.Map, props, containerRef.current);\n                    }\n                    contextValue.map = (0,_mapbox_create_ref_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(mapbox);\n                    contextValue.mapLib = mapboxgl;\n                    setMapInstance(mapbox);\n                    mountedMapsContext?.onMapMount(contextValue.map, props.id);\n                }\n            }[\"_Map.useEffect\"]).catch({\n                \"_Map.useEffect\": (error)=>{\n                    const { onError } = props;\n                    if (onError) {\n                        onError({\n                            type: 'error',\n                            target: null,\n                            error,\n                            originalEvent: error\n                        });\n                    } else {\n                        console.error(error); // eslint-disable-line\n                    }\n                }\n            }[\"_Map.useEffect\"]);\n            return ({\n                \"_Map.useEffect\": ()=>{\n                    isMounted = false;\n                    if (mapbox) {\n                        mountedMapsContext?.onMapUnmount(props.id);\n                        if (props.reuseMaps) {\n                            mapbox.recycle();\n                        } else {\n                            mapbox.destroy();\n                        }\n                    }\n                }\n            })[\"_Map.useEffect\"];\n        }\n    }[\"_Map.useEffect\"], []);\n    (0,_utils_use_isomorphic_layout_effect_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])({\n        \"_Map.useIsomorphicLayoutEffect\": ()=>{\n            if (mapInstance) {\n                mapInstance.setProps(props);\n            }\n        }\n    }[\"_Map.useIsomorphicLayoutEffect\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, {\n        \"_Map.useImperativeHandle\": ()=>contextValue.map\n    }[\"_Map.useImperativeHandle\"], [\n        mapInstance\n    ]);\n    const style = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"_Map.useMemo[style]\": ()=>({\n                position: 'relative',\n                width: '100%',\n                height: '100%',\n                ...props.style\n            })\n    }[\"_Map.useMemo[style]\"], [\n        props.style\n    ]);\n    const CHILD_CONTAINER_STYLE = {\n        height: '100%'\n    };\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        id: props.id,\n        ref: containerRef,\n        style: style\n    }, mapInstance && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(MapContext.Provider, {\n        value: contextValue\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"mapboxgl-children\": \"\",\n        style: CHILD_CONTAINER_STYLE\n    }, props.children)));\n}\nconst Map = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(_Map); //# sourceMappingURL=map.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/marker.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/marker.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Marker: () => (/* binding */ Marker)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/apply-react-style.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/apply-react-style.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/map.js\");\n/* harmony import */ var _utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/deep-equal.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/deep-equal.js\");\n/* global document */ \n\n\n\n\n\n/* eslint-disable complexity,max-statements */ const Marker = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref)=>{\n    const { map, mapLib } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_map_js__WEBPACK_IMPORTED_MODULE_3__.MapContext);\n    const thisRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        props\n    });\n    thisRef.current.props = props;\n    const marker = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"Marker.useMemo[marker]\": ()=>{\n            let hasChildren = false;\n            react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(props.children, {\n                \"Marker.useMemo[marker]\": (el)=>{\n                    if (el) {\n                        hasChildren = true;\n                    }\n                }\n            }[\"Marker.useMemo[marker]\"]);\n            const options = {\n                ...props,\n                element: hasChildren ? document.createElement('div') : null\n            };\n            const mk = new mapLib.Marker(options);\n            mk.setLngLat([\n                props.longitude,\n                props.latitude\n            ]);\n            mk.getElement().addEventListener('click', {\n                \"Marker.useMemo[marker]\": (e)=>{\n                    thisRef.current.props.onClick?.({\n                        type: 'click',\n                        target: mk,\n                        originalEvent: e\n                    });\n                }\n            }[\"Marker.useMemo[marker]\"]);\n            mk.on('dragstart', {\n                \"Marker.useMemo[marker]\": (e)=>{\n                    const evt = e;\n                    evt.lngLat = marker.getLngLat();\n                    thisRef.current.props.onDragStart?.(evt);\n                }\n            }[\"Marker.useMemo[marker]\"]);\n            mk.on('drag', {\n                \"Marker.useMemo[marker]\": (e)=>{\n                    const evt = e;\n                    evt.lngLat = marker.getLngLat();\n                    thisRef.current.props.onDrag?.(evt);\n                }\n            }[\"Marker.useMemo[marker]\"]);\n            mk.on('dragend', {\n                \"Marker.useMemo[marker]\": (e)=>{\n                    const evt = e;\n                    evt.lngLat = marker.getLngLat();\n                    thisRef.current.props.onDragEnd?.(evt);\n                }\n            }[\"Marker.useMemo[marker]\"]);\n            return mk;\n        }\n    }[\"Marker.useMemo[marker]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Marker.useEffect\": ()=>{\n            marker.addTo(map.getMap());\n            return ({\n                \"Marker.useEffect\": ()=>{\n                    marker.remove();\n                }\n            })[\"Marker.useEffect\"];\n        }\n    }[\"Marker.useEffect\"], []);\n    const { longitude, latitude, offset, style, draggable = false, popup = null, rotation = 0, rotationAlignment = 'auto', pitchAlignment = 'auto' } = props;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Marker.useEffect\": ()=>{\n            (0,_utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_2__.applyReactStyle)(marker.getElement(), style);\n        }\n    }[\"Marker.useEffect\"], [\n        style\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, {\n        \"Marker.useImperativeHandle\": ()=>marker\n    }[\"Marker.useImperativeHandle\"], []);\n    if (marker.getLngLat().lng !== longitude || marker.getLngLat().lat !== latitude) {\n        marker.setLngLat([\n            longitude,\n            latitude\n        ]);\n    }\n    if (offset && !(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_4__.arePointsEqual)(marker.getOffset(), offset)) {\n        marker.setOffset(offset);\n    }\n    if (marker.isDraggable() !== draggable) {\n        marker.setDraggable(draggable);\n    }\n    if (marker.getRotation() !== rotation) {\n        marker.setRotation(rotation);\n    }\n    if (marker.getRotationAlignment() !== rotationAlignment) {\n        marker.setRotationAlignment(rotationAlignment);\n    }\n    if (marker.getPitchAlignment() !== pitchAlignment) {\n        marker.setPitchAlignment(pitchAlignment);\n    }\n    if (marker.getPopup() !== popup) {\n        marker.setPopup(popup);\n    }\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(props.children, marker.getElement());\n})); //# sourceMappingURL=marker.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/marker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/navigation-control.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/navigation-control.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NavigationControl: () => (/* binding */ NavigationControl)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/apply-react-style.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/apply-react-style.js\");\n/* harmony import */ var _use_control_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-control.js\");\n\n\n\nfunction _NavigationControl(props) {\n    const ctrl = (0,_use_control_js__WEBPACK_IMPORTED_MODULE_2__.useControl)({\n        \"_NavigationControl.useControl[ctrl]\": ({ mapLib })=>new mapLib.NavigationControl(props)\n    }[\"_NavigationControl.useControl[ctrl]\"], {\n        position: props.position\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"_NavigationControl.useEffect\": ()=>{\n            // @ts-expect-error accessing private member\n            (0,_utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_1__.applyReactStyle)(ctrl._container, props.style);\n        }\n    }[\"_NavigationControl.useEffect\"], [\n        props.style\n    ]);\n    return null;\n}\nconst NavigationControl = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(_NavigationControl); //# sourceMappingURL=navigation-control.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS9jb21wb25lbnRzL25hdmlnYXRpb24tY29udHJvbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQ3NDO0FBQ3FCO0FBQ2xCO0FBV3pDLFNBQVMsa0JBQWtCLENBQUMsS0FBNkI7SUFDdkQsTUFBTSxJQUFJLEdBQUcsMkRBQVU7K0NBQUMsQ0FBQyxFQUFDLE1BQU0sRUFBQyxFQUFFLENBQUcsQ0FBRCxHQUFLLE1BQU0sQ0FBQyxpQkFBaUIsQ0FBQyxLQUFLLENBQUM7OENBQUU7UUFDekUsUUFBUSxFQUFFLEtBQUssQ0FBQyxRQUFRO0tBQ3pCLENBQUMsQ0FBQztJQUVILGdEQUFTO3dDQUFDLEdBQUcsRUFBRTtZQUNiLDRDQUE0QztZQUM1Qyw0RUFBZSxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsS0FBSyxDQUFDLEtBQUssQ0FBQyxDQUFDO1FBQ2hELENBQUM7dUNBQUU7UUFBQyxLQUFLLENBQUMsS0FBSztLQUFDLENBQUMsQ0FBQztJQUVsQixPQUFPLElBQUksQ0FBQztBQUNkLENBQUM7QUFFTSxNQUFNLGlCQUFpQixpQkFBRywyQ0FBSSxDQUFDLGtCQUFrQixDQUFDLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxzcmNcXG1hcGJveC1sZWdhY3lcXGNvbXBvbmVudHNcXG5hdmlnYXRpb24tY29udHJvbC50cyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/navigation-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/popup.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/popup.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Popup: () => (/* binding */ Popup)\n/* harmony export */ });\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/apply-react-style.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/apply-react-style.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/map.js\");\n/* harmony import */ var _utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/deep-equal.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/deep-equal.js\");\n\n\n\n\n\n// Adapted from https://github.com/mapbox/mapbox-gl-js/blob/v1.13.0/src/ui/popup.js\nfunction getClassList(className) {\n    return new Set(className ? className.trim().split(/\\s+/) : []);\n}\n/* eslint-disable complexity,max-statements */ const Popup = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)((props, ref)=>{\n    const { map, mapLib } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_map_js__WEBPACK_IMPORTED_MODULE_3__.MapContext);\n    const container = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Popup.useMemo[container]\": ()=>{\n            return document.createElement('div');\n        }\n    }[\"Popup.useMemo[container]\"], []);\n    const thisRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        props\n    });\n    thisRef.current.props = props;\n    const popup = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"Popup.useMemo[popup]\": ()=>{\n            const options = {\n                ...props\n            };\n            const pp = new mapLib.Popup(options);\n            pp.setLngLat([\n                props.longitude,\n                props.latitude\n            ]);\n            pp.once('open', {\n                \"Popup.useMemo[popup]\": (e)=>{\n                    thisRef.current.props.onOpen?.(e);\n                }\n            }[\"Popup.useMemo[popup]\"]);\n            return pp;\n        }\n    }[\"Popup.useMemo[popup]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Popup.useEffect\": ()=>{\n            const onClose = {\n                \"Popup.useEffect.onClose\": (e)=>{\n                    thisRef.current.props.onClose?.(e);\n                }\n            }[\"Popup.useEffect.onClose\"];\n            popup.on('close', onClose);\n            popup.setDOMContent(container).addTo(map.getMap());\n            return ({\n                \"Popup.useEffect\": ()=>{\n                    // https://github.com/visgl/react-map-gl/issues/1825\n                    // onClose should not be fired if the popup is removed by unmounting\n                    // When using React strict mode, the component is mounted twice.\n                    // Firing the onClose callback here would be a false signal to remove the component.\n                    popup.off('close', onClose);\n                    if (popup.isOpen()) {\n                        popup.remove();\n                    }\n                }\n            })[\"Popup.useEffect\"];\n        }\n    }[\"Popup.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Popup.useEffect\": ()=>{\n            (0,_utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_2__.applyReactStyle)(popup.getElement(), props.style);\n        }\n    }[\"Popup.useEffect\"], [\n        props.style\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"Popup.useImperativeHandle\": ()=>popup\n    }[\"Popup.useImperativeHandle\"], []);\n    if (popup.isOpen()) {\n        if (popup.getLngLat().lng !== props.longitude || popup.getLngLat().lat !== props.latitude) {\n            popup.setLngLat([\n                props.longitude,\n                props.latitude\n            ]);\n        }\n        if (props.offset && !(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_4__.deepEqual)(popup.options.offset, props.offset)) {\n            popup.setOffset(props.offset);\n        }\n        if (popup.options.anchor !== props.anchor || popup.options.maxWidth !== props.maxWidth) {\n            popup.options.anchor = props.anchor;\n            popup.setMaxWidth(props.maxWidth);\n        }\n        if (popup.options.className !== props.className) {\n            const prevClassList = getClassList(popup.options.className);\n            const nextClassList = getClassList(props.className);\n            for (const c of prevClassList){\n                if (!nextClassList.has(c)) {\n                    popup.removeClassName(c);\n                }\n            }\n            for (const c of nextClassList){\n                if (!prevClassList.has(c)) {\n                    popup.addClassName(c);\n                }\n            }\n            popup.options.className = props.className;\n        }\n    }\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_0__.createPortal)(props.children, container);\n})); //# sourceMappingURL=popup.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/popup.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/scale-control.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/scale-control.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScaleControl: () => (/* binding */ ScaleControl)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/apply-react-style.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/apply-react-style.js\");\n/* harmony import */ var _use_control_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./use-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-control.js\");\n\n\n\nfunction _ScaleControl(props) {\n    const ctrl = (0,_use_control_js__WEBPACK_IMPORTED_MODULE_2__.useControl)({\n        \"_ScaleControl.useControl[ctrl]\": ({ mapLib })=>new mapLib.ScaleControl(props)\n    }[\"_ScaleControl.useControl[ctrl]\"], {\n        position: props.position\n    });\n    const propsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props);\n    const prevProps = propsRef.current;\n    propsRef.current = props;\n    const { style } = props;\n    if (props.maxWidth !== undefined && props.maxWidth !== prevProps.maxWidth) {\n        // @ts-expect-error accessing private member\n        ctrl.options.maxWidth = props.maxWidth;\n    }\n    if (props.unit !== undefined && props.unit !== prevProps.unit) {\n        ctrl.setUnit(props.unit);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"_ScaleControl.useEffect\": ()=>{\n            // @ts-expect-error accessing private member\n            (0,_utils_apply_react_style_js__WEBPACK_IMPORTED_MODULE_1__.applyReactStyle)(ctrl._container, style);\n        }\n    }[\"_ScaleControl.useEffect\"], [\n        style\n    ]);\n    return null;\n}\nconst ScaleControl = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(_ScaleControl); //# sourceMappingURL=scale-control.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS9jb21wb25lbnRzL3NjYWxlLWNvbnRyb2wuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUM4QztBQUNhO0FBQ2xCO0FBV3pDLFNBQVMsYUFBYSxDQUFDLEtBQXdCO0lBQzdDLE1BQU0sSUFBSSxHQUFHLDJEQUFVOzBDQUFDLENBQUMsRUFBQyxNQUFNLEVBQUMsRUFBRSxDQUFHLENBQUQsR0FBSyxNQUFNLENBQUMsWUFBWSxDQUFDLEtBQUssQ0FBQzt5Q0FBRTtRQUNwRSxRQUFRLEVBQUUsS0FBSyxDQUFDLFFBQVE7S0FDekIsQ0FBQyxDQUFDO0lBQ0gsTUFBTSxRQUFRLEdBQUcsNkNBQU0sQ0FBb0IsS0FBSyxDQUFDLENBQUM7SUFFbEQsTUFBTSxTQUFTLEdBQUcsUUFBUSxDQUFDLE9BQU8sQ0FBQztJQUNuQyxRQUFRLENBQUMsT0FBTyxHQUFHLEtBQUssQ0FBQztJQUV6QixNQUFNLEVBQUMsS0FBSyxFQUFDLEdBQUcsS0FBSyxDQUFDO0lBRXRCLElBQUksS0FBSyxDQUFDLFFBQVEsS0FBSyxTQUFTLElBQUksS0FBSyxDQUFDLFFBQVEsS0FBSyxTQUFTLENBQUMsUUFBUSxFQUFFLENBQUM7UUFDMUUsNENBQTRDO1FBQzVDLElBQUksQ0FBQyxPQUFPLENBQUMsUUFBUSxHQUFHLEtBQUssQ0FBQyxRQUFRLENBQUM7SUFDekMsQ0FBQztJQUNELElBQUksS0FBSyxDQUFDLElBQUksS0FBSyxTQUFTLElBQUksS0FBSyxDQUFDLElBQUksS0FBSyxTQUFTLENBQUMsSUFBSSxFQUFFLENBQUM7UUFDOUQsSUFBSSxDQUFDLE9BQU8sQ0FBQyxLQUFLLENBQUMsSUFBVyxDQUFDLENBQUM7SUFDbEMsQ0FBQztJQUVELGdEQUFTO21DQUFDLEdBQUcsRUFBRTtZQUNiLDRDQUE0QztZQUM1Qyw0RUFBZSxDQUFDLElBQUksQ0FBQyxVQUFVLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDMUMsQ0FBQztrQ0FBRTtRQUFDLEtBQUs7S0FBQyxDQUFDLENBQUM7SUFFWixPQUFPLElBQUksQ0FBQztBQUNkLENBQUM7QUFFTSxNQUFNLFlBQVksaUJBQUcsMkNBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEb2N1bWVudHNcXHNyY1xcbWFwYm94LWxlZ2FjeVxcY29tcG9uZW50c1xcc2NhbGUtY29udHJvbC50cyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/scale-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/source.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/source.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Source: () => (/* binding */ Source)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/map.js\");\n/* harmony import */ var _utils_assert_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/assert.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/assert.js\");\n/* harmony import */ var _utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/deep-equal.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/deep-equal.js\");\n\n\n\n\n\nlet sourceCounter = 0;\nfunction createSource(map, id, props) {\n    // @ts-ignore\n    if (map.style && map.style._loaded) {\n        const options = {\n            ...props\n        };\n        delete options.id;\n        delete options.children;\n        // @ts-ignore\n        map.addSource(id, options);\n        return map.getSource(id);\n    }\n    return null;\n}\n/* eslint-disable complexity */ function updateSource(source, props, prevProps) {\n    (0,_utils_assert_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props.id === prevProps.id, 'source id changed');\n    (0,_utils_assert_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(props.type === prevProps.type, 'source type changed');\n    let changedKey = '';\n    let changedKeyCount = 0;\n    for(const key in props){\n        if (key !== 'children' && key !== 'id' && !(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_3__.deepEqual)(prevProps[key], props[key])) {\n            changedKey = key;\n            changedKeyCount++;\n        }\n    }\n    if (!changedKeyCount) {\n        return;\n    }\n    const type = props.type;\n    if (type === 'geojson') {\n        source.setData(props.data);\n    } else if (type === 'image') {\n        source.updateImage({\n            url: props.url,\n            coordinates: props.coordinates\n        });\n    } else if ('setCoordinates' in source && changedKeyCount === 1 && changedKey === 'coordinates') {\n        source.setCoordinates(props.coordinates);\n    } else if ('setUrl' in source && changedKey === 'url') {\n        source.setUrl(props.url);\n    } else if ('setTiles' in source && changedKey === 'tiles') {\n        source.setTiles(props.tiles);\n    } else {\n        // eslint-disable-next-line\n        console.warn(`Unable to update <Source> prop: ${changedKey}`);\n    }\n}\n/* eslint-enable complexity */ function Source(props) {\n    const map = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_map_js__WEBPACK_IMPORTED_MODULE_1__.MapContext).map.getMap();\n    const propsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(props);\n    const [, setStyleLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"Source.useMemo[id]\": ()=>props.id || `jsx-source-${sourceCounter++}`\n    }[\"Source.useMemo[id]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Source.useEffect\": ()=>{\n            if (map) {\n                /* global setTimeout */ const forceUpdate = {\n                    \"Source.useEffect.forceUpdate\": ()=>setTimeout({\n                            \"Source.useEffect.forceUpdate\": ()=>setStyleLoaded({\n                                    \"Source.useEffect.forceUpdate\": (version)=>version + 1\n                                }[\"Source.useEffect.forceUpdate\"])\n                        }[\"Source.useEffect.forceUpdate\"], 0)\n                }[\"Source.useEffect.forceUpdate\"];\n                map.on('styledata', forceUpdate);\n                forceUpdate();\n                return ({\n                    \"Source.useEffect\": ()=>{\n                        map.off('styledata', forceUpdate);\n                        // @ts-ignore\n                        if (map.style && map.style._loaded && map.getSource(id)) {\n                            // Parent effects are destroyed before child ones, see\n                            // https://github.com/facebook/react/issues/16728\n                            // Source can only be removed after all child layers are removed\n                            const allLayers = map.getStyle()?.layers;\n                            if (allLayers) {\n                                for (const layer of allLayers){\n                                    // @ts-ignore (2339) source does not exist on all layer types\n                                    if (layer.source === id) {\n                                        map.removeLayer(layer.id);\n                                    }\n                                }\n                            }\n                            map.removeSource(id);\n                        }\n                    }\n                })[\"Source.useEffect\"];\n            }\n            return undefined;\n        }\n    }[\"Source.useEffect\"], [\n        map\n    ]);\n    // @ts-ignore\n    let source = map && map.style && map.getSource(id);\n    if (source) {\n        updateSource(source, props, propsRef.current);\n    } else {\n        source = createSource(map, id, props);\n    }\n    propsRef.current = props;\n    return source && react__WEBPACK_IMPORTED_MODULE_0__.Children.map(props.children, (child)=>child && /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {\n            source: id\n        })) || null;\n} //# sourceMappingURL=source.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/source.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-control.js":
/*!********************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/use-control.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControl: () => (/* binding */ useControl)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/map.js\");\n\n\nfunction useControl(onCreate, arg1, arg2, arg3) {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_map_js__WEBPACK_IMPORTED_MODULE_1__.MapContext);\n    const ctrl = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useControl.useMemo[ctrl]\": ()=>onCreate(context)\n    }[\"useControl.useMemo[ctrl]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useControl.useEffect\": ()=>{\n            const opts = arg3 || arg2 || arg1;\n            const onAdd = typeof arg1 === 'function' && typeof arg2 === 'function' ? arg1 : null;\n            const onRemove = typeof arg2 === 'function' ? arg2 : typeof arg1 === 'function' ? arg1 : null;\n            const { map } = context;\n            if (!map.hasControl(ctrl)) {\n                map.addControl(ctrl, opts?.position);\n                if (onAdd) {\n                    onAdd(context);\n                }\n            }\n            return ({\n                \"useControl.useEffect\": ()=>{\n                    if (onRemove) {\n                        onRemove(context);\n                    }\n                    // Map might have been removed (parent effects are destroyed before child ones)\n                    if (map.hasControl(ctrl)) {\n                        map.removeControl(ctrl);\n                    }\n                }\n            })[\"useControl.useEffect\"];\n        }\n    }[\"useControl.useEffect\"], []);\n    return ctrl;\n} //# sourceMappingURL=use-control.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS9jb21wb25lbnRzL3VzZS1jb250cm9sLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDtBQUVwQjtBQXlCM0IsU0FBVSxVQUFVLENBQ3hCLFFBQXlDLEVBQ3pDLElBQTRELEVBQzVELElBQTRELEVBQzVELElBQXFCO0lBRXJCLE1BQU0sT0FBTyxHQUFHLGlEQUFVLENBQUMsK0NBQVUsQ0FBQyxDQUFDO0lBQ3ZDLE1BQU0sSUFBSSxHQUFHLDhDQUFPO29DQUFDLEdBQUcsQ0FBRyxDQUFELE9BQVMsQ0FBQyxPQUFPLENBQUM7bUNBQUUsRUFBRSxDQUFDLENBQUM7SUFFbEQsZ0RBQVM7Z0NBQUMsR0FBRyxFQUFFO1lBQ2IsTUFBTSxJQUFJLEdBQUksSUFBSSxJQUFJLElBQUksSUFBSSxJQUFJLENBQW1CO1lBQ3JELE1BQU0sS0FBSyxHQUFHLE9BQU8sSUFBSSxLQUFLLFVBQVUsSUFBSSxPQUFPLElBQUksS0FBSyxVQUFVLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO1lBQ3JGLE1BQU0sUUFBUSxHQUFHLE9BQU8sSUFBSSxLQUFLLFVBQVUsQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxPQUFPLElBQUksS0FBSyxVQUFVLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO1lBRTlGLE1BQU0sRUFBQyxHQUFHLEVBQUMsR0FBRyxPQUFPLENBQUM7WUFDdEIsSUFBSSxDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQztnQkFDMUIsR0FBRyxDQUFDLFVBQVUsQ0FBQyxJQUFJLEVBQUUsSUFBSSxFQUFFLFFBQVEsQ0FBQyxDQUFDO2dCQUNyQyxJQUFJLEtBQUssRUFBRSxDQUFDO29CQUNWLEtBQUssQ0FBQyxPQUFPLENBQUMsQ0FBQztnQkFDakIsQ0FBQztZQUNILENBQUM7WUFFRDt3Q0FBTyxHQUFHLEVBQUU7b0JBQ1YsSUFBSSxRQUFRLEVBQUUsQ0FBQzt3QkFDYixRQUFRLENBQUMsT0FBTyxDQUFDLENBQUM7b0JBQ3BCLENBQUM7b0JBQ0QsK0VBQStFO29CQUMvRSxJQUFJLEdBQUcsQ0FBQyxVQUFVLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQzt3QkFDekIsR0FBRyxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsQ0FBQztvQkFDMUIsQ0FBQztnQkFDSCxDQUFDOztRQUNILENBQUM7K0JBQUUsRUFBRSxDQUFDLENBQUM7SUFFUCxPQUFPLElBQUksQ0FBQztBQUNkLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxzcmNcXG1hcGJveC1sZWdhY3lcXGNvbXBvbmVudHNcXHVzZS1jb250cm9sLnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-control.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-map.js":
/*!****************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/components/use-map.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MapProvider: () => (/* binding */ MapProvider),\n/* harmony export */   MountedMapsContext: () => (/* binding */ MountedMapsContext),\n/* harmony export */   useMap: () => (/* binding */ useMap)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/map.js\");\n\n\n\nconst MountedMapsContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\nconst MapProvider = (props)=>{\n    const [maps, setMaps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({});\n    const onMapMount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"MapProvider.useCallback[onMapMount]\": (map, id = 'default')=>{\n            setMaps({\n                \"MapProvider.useCallback[onMapMount]\": (currMaps)=>{\n                    if (id === 'current') {\n                        throw new Error(\"'current' cannot be used as map id\");\n                    }\n                    if (currMaps[id]) {\n                        throw new Error(`Multiple maps with the same id: ${id}`);\n                    }\n                    return {\n                        ...currMaps,\n                        [id]: map\n                    };\n                }\n            }[\"MapProvider.useCallback[onMapMount]\"]);\n        }\n    }[\"MapProvider.useCallback[onMapMount]\"], []);\n    const onMapUnmount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"MapProvider.useCallback[onMapUnmount]\": (id = 'default')=>{\n            setMaps({\n                \"MapProvider.useCallback[onMapUnmount]\": (currMaps)=>{\n                    if (currMaps[id]) {\n                        const nextMaps = {\n                            ...currMaps\n                        };\n                        delete nextMaps[id];\n                        return nextMaps;\n                    }\n                    return currMaps;\n                }\n            }[\"MapProvider.useCallback[onMapUnmount]\"]);\n        }\n    }[\"MapProvider.useCallback[onMapUnmount]\"], []);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(MountedMapsContext.Provider, {\n        value: {\n            maps,\n            onMapMount,\n            onMapUnmount\n        }\n    }, props.children);\n};\nfunction useMap() {\n    const maps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(MountedMapsContext)?.maps;\n    const currentMap = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_map_js__WEBPACK_IMPORTED_MODULE_1__.MapContext);\n    const mapsWithCurrent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useMap.useMemo[mapsWithCurrent]\": ()=>{\n            return {\n                ...maps,\n                current: currentMap?.map\n            };\n        }\n    }[\"useMap.useMemo[mapsWithCurrent]\"], [\n        maps,\n        currentMap\n    ]);\n    return mapsWithCurrent;\n} //# sourceMappingURL=use-map.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS9jb21wb25lbnRzL3VzZS1tYXAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBK0I7QUFDa0M7QUFHaEM7QUFRMUIsTUFBTSxrQkFBa0IsaUJBQUcsZ0RBQW1CLENBQTBCLElBQUksQ0FBQyxDQUFDO0FBRTlFLE1BQU0sV0FBVyxJQUEyQyxLQUFLLENBQUMsRUFBRTtJQUN6RSxNQUFNLENBQUMsSUFBSSxFQUFFLE9BQU8sQ0FBQyxHQUFHLCtDQUFRLENBQXlCLEVBQUUsQ0FBQyxDQUFDO0lBRTdELE1BQU0sVUFBVSxHQUFHLGtEQUFXOytDQUFDLENBQUMsR0FBVyxFQUFFLEtBQWEsU0FBUyxFQUFFLEVBQUU7WUFDckUsT0FBTzt3REFBQyxRQUFRLENBQUMsRUFBRTtvQkFDakIsSUFBSSxFQUFFLEtBQUssU0FBUyxFQUFFLENBQUM7d0JBQ3JCLE1BQU0sSUFBSSxLQUFLLENBQUMsb0NBQW9DLENBQUMsQ0FBQztvQkFDeEQsQ0FBQztvQkFDRCxJQUFJLFFBQVEsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDO3dCQUNqQixNQUFNLElBQUksS0FBSyxDQUFDLG1DQUFtQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO29CQUMzRCxDQUFDO29CQUNELE9BQU87d0JBQUMsR0FBRyxRQUFRO3dCQUFFLENBQUMsRUFBRSxDQUFDLEVBQUUsR0FBRztvQkFBQSxDQUFDLENBQUM7Z0JBQ2xDLENBQUM7O1FBQ0gsQ0FBQzs4Q0FBRSxFQUFFLENBQUMsQ0FBQztJQUVQLE1BQU0sWUFBWSxHQUFHLGtEQUFXO2lEQUFDLENBQUMsS0FBYSxTQUFTLEVBQUUsRUFBRTtZQUMxRCxPQUFPOzBEQUFDLFFBQVEsQ0FBQyxFQUFFO29CQUNqQixJQUFJLFFBQVEsQ0FBQyxFQUFFLENBQUMsRUFBRSxDQUFDO3dCQUNqQixNQUFNLFFBQVEsR0FBRzs0QkFBQyxHQUFHLFFBQVE7d0JBQUEsQ0FBQyxDQUFDO3dCQUMvQixPQUFPLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQzt3QkFDcEIsT0FBTyxRQUFRLENBQUM7b0JBQ2xCLENBQUM7b0JBQ0QsT0FBTyxRQUFRLENBQUM7Z0JBQ2xCLENBQUM7O1FBQ0gsQ0FBQztnREFBRSxFQUFFLENBQUMsQ0FBQztJQUVQLE9BQU8sY0FDTCxpREFBQyxrQkFBa0IsQ0FBQyxRQUFRO1FBQzFCLEtBQUssRUFBRTtZQUNMLElBQUk7WUFDSixVQUFVO1lBQ1YsWUFBWTtTQUNiO0lBQUEsR0FFQSxLQUFLLENBQUMsUUFBUSxDQUNhLENBQy9CO0FBQ0gsQ0FBQyxDQUFDO0FBT0ksU0FBVSxNQUFNO0lBQ3BCLE1BQU0sSUFBSSxHQUFHLGlEQUFVLENBQUMsa0JBQWtCLENBQUMsRUFBRSxJQUFJLENBQUM7SUFDbEQsTUFBTSxVQUFVLEdBQUcsaURBQVUsQ0FBQywrQ0FBVSxDQUFDLENBQUM7SUFFMUMsTUFBTSxlQUFlLEdBQUcsOENBQU87MkNBQUMsR0FBRyxFQUFFO1lBQ25DLE9BQU87Z0JBQUMsR0FBRyxJQUFJO2dCQUFFLE9BQU8sRUFBRSxVQUFVLEVBQUUsR0FBRztZQUFBLENBQUMsQ0FBQztRQUM3QyxDQUFDOzBDQUFFO1FBQUMsSUFBSTtRQUFFLFVBQVU7S0FBQyxDQUFDLENBQUM7SUFFdkIsT0FBTyxlQUFnQyxDQUFDO0FBQzFDLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxzcmNcXG1hcGJveC1sZWdhY3lcXGNvbXBvbmVudHNcXHVzZS1tYXAudHN4Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AttributionControl: () => (/* reexport safe */ _components_attribution_control_js__WEBPACK_IMPORTED_MODULE_3__.AttributionControl),\n/* harmony export */   FullscreenControl: () => (/* reexport safe */ _components_fullscreen_control_js__WEBPACK_IMPORTED_MODULE_4__.FullscreenControl),\n/* harmony export */   GeolocateControl: () => (/* reexport safe */ _components_geolocate_control_js__WEBPACK_IMPORTED_MODULE_5__.GeolocateControl),\n/* harmony export */   Layer: () => (/* reexport safe */ _components_layer_js__WEBPACK_IMPORTED_MODULE_9__.Layer),\n/* harmony export */   Map: () => (/* reexport safe */ _components_map_js__WEBPACK_IMPORTED_MODULE_0__.Map),\n/* harmony export */   MapProvider: () => (/* reexport safe */ _components_use_map_js__WEBPACK_IMPORTED_MODULE_11__.MapProvider),\n/* harmony export */   Marker: () => (/* reexport safe */ _components_marker_js__WEBPACK_IMPORTED_MODULE_1__.Marker),\n/* harmony export */   NavigationControl: () => (/* reexport safe */ _components_navigation_control_js__WEBPACK_IMPORTED_MODULE_6__.NavigationControl),\n/* harmony export */   Popup: () => (/* reexport safe */ _components_popup_js__WEBPACK_IMPORTED_MODULE_2__.Popup),\n/* harmony export */   ScaleControl: () => (/* reexport safe */ _components_scale_control_js__WEBPACK_IMPORTED_MODULE_7__.ScaleControl),\n/* harmony export */   Source: () => (/* reexport safe */ _components_source_js__WEBPACK_IMPORTED_MODULE_8__.Source),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useControl: () => (/* reexport safe */ _components_use_control_js__WEBPACK_IMPORTED_MODULE_10__.useControl),\n/* harmony export */   useMap: () => (/* reexport safe */ _components_use_map_js__WEBPACK_IMPORTED_MODULE_11__.useMap)\n/* harmony export */ });\n/* harmony import */ var _components_map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./components/map.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/map.js\");\n/* harmony import */ var _components_marker_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./components/marker.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/marker.js\");\n/* harmony import */ var _components_popup_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/popup.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/popup.js\");\n/* harmony import */ var _components_attribution_control_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/attribution-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/attribution-control.js\");\n/* harmony import */ var _components_fullscreen_control_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/fullscreen-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/fullscreen-control.js\");\n/* harmony import */ var _components_geolocate_control_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./components/geolocate-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/geolocate-control.js\");\n/* harmony import */ var _components_navigation_control_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./components/navigation-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/navigation-control.js\");\n/* harmony import */ var _components_scale_control_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./components/scale-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/scale-control.js\");\n/* harmony import */ var _components_source_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./components/source.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/source.js\");\n/* harmony import */ var _components_layer_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./components/layer.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/layer.js\");\n/* harmony import */ var _components_use_control_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./components/use-control.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-control.js\");\n/* harmony import */ var _components_use_map_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./components/use-map.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/components/use-map.js\");\n/* harmony import */ var _types_common_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./types/common.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/types/common.js\");\n/* harmony import */ var _types_events_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./types/events.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/types/events.js\");\n/* harmony import */ var _types_lib_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./types/lib.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/types/lib.js\");\n/* harmony import */ var _types_style_spec_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./types/style-spec.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/types/style-spec.js\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_components_map_js__WEBPACK_IMPORTED_MODULE_0__.Map);\n\n\n\n\n\n\n\n\n\n\n\n// Types\n\n\n\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBcUM7QUFDeEI7QUFDYixpRUFBZSxtREFBRyxFQUFDO0FBRXdCO0FBQ0Y7QUFDMkI7QUFDRjtBQUNGO0FBQ0U7QUFDVjtBQUNiO0FBQ0Y7QUFDVztBQUNLO0FBY3pELFFBQVE7QUFDdUI7QUFDQTtBQUNIO0FBQ08iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxzcmNcXG1hcGJveC1sZWdhY3lcXGluZGV4LnRzIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/mapbox/create-ref.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/mapbox/create-ref.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createRef)\n/* harmony export */ });\n/** These methods may break the react binding if called directly */ const skipMethods = [\n    'setMaxBounds',\n    'setMinZoom',\n    'setMaxZoom',\n    'setMinPitch',\n    'setMaxPitch',\n    'setRenderWorldCopies',\n    'setProjection',\n    'setStyle',\n    'addSource',\n    'removeSource',\n    'addLayer',\n    'removeLayer',\n    'setLayerZoomRange',\n    'setFilter',\n    'setPaintProperty',\n    'setLayoutProperty',\n    'setLight',\n    'setTerrain',\n    'setFog',\n    'remove'\n];\nfunction createRef(mapInstance) {\n    if (!mapInstance) {\n        return null;\n    }\n    const map = mapInstance.map;\n    const ref = {\n        getMap: ()=>mapInstance.map,\n        // Overwrite getters to use our shadow transform\n        getCenter: ()=>mapInstance.transform.center,\n        getZoom: ()=>mapInstance.transform.zoom,\n        getBearing: ()=>mapInstance.transform.bearing,\n        getPitch: ()=>mapInstance.transform.pitch,\n        getPadding: ()=>mapInstance.transform.padding,\n        getBounds: ()=>mapInstance.transform.getBounds(),\n        project: (lnglat)=>{\n            const tr = map.transform;\n            map.transform = mapInstance.transform;\n            const result = map.project(lnglat);\n            map.transform = tr;\n            return result;\n        },\n        unproject: (point)=>{\n            const tr = map.transform;\n            map.transform = mapInstance.transform;\n            const result = map.unproject(point);\n            map.transform = tr;\n            return result;\n        },\n        // options diverge between mapbox and maplibre\n        queryTerrainElevation: (lnglat, options)=>{\n            const tr = map.transform;\n            map.transform = mapInstance.transform;\n            const result = map.queryTerrainElevation(lnglat, options);\n            map.transform = tr;\n            return result;\n        },\n        queryRenderedFeatures: (geometry, options)=>{\n            const tr = map.transform;\n            map.transform = mapInstance.transform;\n            const result = map.queryRenderedFeatures(geometry, options);\n            map.transform = tr;\n            return result;\n        }\n    };\n    for (const key of getMethodNames(map)){\n        // @ts-expect-error\n        if (!(key in ref) && !skipMethods.includes(key)) {\n            ref[key] = map[key].bind(map);\n        }\n    }\n    return ref;\n}\nfunction getMethodNames(obj) {\n    const result = new Set();\n    let proto = obj;\n    while(proto){\n        for (const key of Object.getOwnPropertyNames(proto)){\n            if (key[0] !== '_' && typeof obj[key] === 'function' && key !== 'fire' && key !== 'setEventedParent') {\n                result.add(key);\n            }\n        }\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Array.from(result);\n} //# sourceMappingURL=create-ref.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/mapbox/create-ref.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/mapbox/mapbox.js":
/*!***********************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/mapbox/mapbox.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_transform_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/transform.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/transform.js\");\n/* harmony import */ var _utils_style_utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/style-utils.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/style-utils.js\");\n/* harmony import */ var _utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/deep-equal.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/deep-equal.js\");\n\n\n\nconst DEFAULT_STYLE = {\n    version: 8,\n    sources: {},\n    layers: []\n};\nconst pointerEvents = {\n    mousedown: 'onMouseDown',\n    mouseup: 'onMouseUp',\n    mouseover: 'onMouseOver',\n    mousemove: 'onMouseMove',\n    click: 'onClick',\n    dblclick: 'onDblClick',\n    mouseenter: 'onMouseEnter',\n    mouseleave: 'onMouseLeave',\n    mouseout: 'onMouseOut',\n    contextmenu: 'onContextMenu',\n    touchstart: 'onTouchStart',\n    touchend: 'onTouchEnd',\n    touchmove: 'onTouchMove',\n    touchcancel: 'onTouchCancel'\n};\nconst cameraEvents = {\n    movestart: 'onMoveStart',\n    move: 'onMove',\n    moveend: 'onMoveEnd',\n    dragstart: 'onDragStart',\n    drag: 'onDrag',\n    dragend: 'onDragEnd',\n    zoomstart: 'onZoomStart',\n    zoom: 'onZoom',\n    zoomend: 'onZoomEnd',\n    rotatestart: 'onRotateStart',\n    rotate: 'onRotate',\n    rotateend: 'onRotateEnd',\n    pitchstart: 'onPitchStart',\n    pitch: 'onPitch',\n    pitchend: 'onPitchEnd'\n};\nconst otherEvents = {\n    wheel: 'onWheel',\n    boxzoomstart: 'onBoxZoomStart',\n    boxzoomend: 'onBoxZoomEnd',\n    boxzoomcancel: 'onBoxZoomCancel',\n    resize: 'onResize',\n    load: 'onLoad',\n    render: 'onRender',\n    idle: 'onIdle',\n    remove: 'onRemove',\n    data: 'onData',\n    styledata: 'onStyleData',\n    sourcedata: 'onSourceData',\n    error: 'onError'\n};\nconst settingNames = [\n    'minZoom',\n    'maxZoom',\n    'minPitch',\n    'maxPitch',\n    'maxBounds',\n    'projection',\n    'renderWorldCopies'\n];\nconst handlerNames = [\n    'scrollZoom',\n    'boxZoom',\n    'dragRotate',\n    'dragPan',\n    'keyboard',\n    'doubleClickZoom',\n    'touchZoomRotate',\n    'touchPitch'\n];\n/**\n * A wrapper for mapbox-gl's Map class\n */ class Mapbox {\n    constructor(MapClass, props, container){\n        // mapboxgl.Map instance\n        this._map = null;\n        // Internal states\n        this._internalUpdate = false;\n        this._inRender = false;\n        this._hoveredFeatures = null;\n        this._deferredEvents = {\n            move: false,\n            zoom: false,\n            pitch: false,\n            rotate: false\n        };\n        this._onEvent = (e)=>{\n            // @ts-ignore\n            const cb = this.props[otherEvents[e.type]];\n            if (cb) {\n                cb(e);\n            } else if (e.type === 'error') {\n                console.error(e.error); // eslint-disable-line\n            }\n        };\n        this._onPointerEvent = (e)=>{\n            if (e.type === 'mousemove' || e.type === 'mouseout') {\n                this._updateHover(e);\n            }\n            // @ts-ignore\n            const cb = this.props[pointerEvents[e.type]];\n            if (cb) {\n                if (this.props.interactiveLayerIds && e.type !== 'mouseover' && e.type !== 'mouseout') {\n                    e.features = this._hoveredFeatures || this._queryRenderedFeatures(e.point);\n                }\n                cb(e);\n                delete e.features;\n            }\n        };\n        this._onCameraEvent = (e)=>{\n            if (!this._internalUpdate) {\n                // @ts-ignore\n                const cb = this.props[cameraEvents[e.type]];\n                if (cb) {\n                    cb(e);\n                }\n            }\n            if (e.type in this._deferredEvents) {\n                this._deferredEvents[e.type] = false;\n            }\n        };\n        this._MapClass = MapClass;\n        this.props = props;\n        this._initialize(container);\n    }\n    get map() {\n        return this._map;\n    }\n    get transform() {\n        return this._renderTransform;\n    }\n    setProps(props) {\n        const oldProps = this.props;\n        this.props = props;\n        const settingsChanged = this._updateSettings(props, oldProps);\n        if (settingsChanged) {\n            this._createShadowTransform(this._map);\n        }\n        const sizeChanged = this._updateSize(props);\n        const viewStateChanged = this._updateViewState(props, true);\n        this._updateStyle(props, oldProps);\n        this._updateStyleComponents(props, oldProps);\n        this._updateHandlers(props, oldProps);\n        // If 1) view state has changed to match props and\n        //    2) the props change is not triggered by map events,\n        // it's driven by an external state change. Redraw immediately\n        if (settingsChanged || sizeChanged || viewStateChanged && !this._map.isMoving()) {\n            this.redraw();\n        }\n    }\n    static reuse(props, container) {\n        const that = Mapbox.savedMaps.pop();\n        if (!that) {\n            return null;\n        }\n        const map = that.map;\n        // When reusing the saved map, we need to reparent the map(canvas) and other child nodes\n        // intoto the new container from the props.\n        // Step 1: reparenting child nodes from old container to new container\n        const oldContainer = map.getContainer();\n        container.className = oldContainer.className;\n        while(oldContainer.childNodes.length > 0){\n            container.appendChild(oldContainer.childNodes[0]);\n        }\n        // Step 2: replace the internal container with new container from the react component\n        // @ts-ignore\n        map._container = container;\n        // Step 4: apply new props\n        that.setProps({\n            ...props,\n            styleDiffing: false\n        });\n        map.resize();\n        const { initialViewState } = props;\n        if (initialViewState) {\n            if (initialViewState.bounds) {\n                map.fitBounds(initialViewState.bounds, {\n                    ...initialViewState.fitBoundsOptions,\n                    duration: 0\n                });\n            } else {\n                that._updateViewState(initialViewState, false);\n            }\n        }\n        // Simulate load event\n        if (map.isStyleLoaded()) {\n            map.fire('load');\n        } else {\n            map.once('styledata', ()=>map.fire('load'));\n        }\n        // Force reload\n        // @ts-ignore\n        map._update();\n        return that;\n    }\n    /* eslint-disable complexity,max-statements */ _initialize(container) {\n        const { props } = this;\n        const { mapStyle = DEFAULT_STYLE } = props;\n        const mapOptions = {\n            ...props,\n            ...props.initialViewState,\n            accessToken: props.mapboxAccessToken || getAccessTokenFromEnv() || null,\n            container,\n            style: (0,_utils_style_utils_js__WEBPACK_IMPORTED_MODULE_1__.normalizeStyle)(mapStyle)\n        };\n        const viewState = mapOptions.initialViewState || mapOptions.viewState || mapOptions;\n        Object.assign(mapOptions, {\n            center: [\n                viewState.longitude || 0,\n                viewState.latitude || 0\n            ],\n            zoom: viewState.zoom || 0,\n            pitch: viewState.pitch || 0,\n            bearing: viewState.bearing || 0\n        });\n        if (props.gl) {\n            // eslint-disable-next-line\n            const getContext = HTMLCanvasElement.prototype.getContext;\n            // Hijack canvas.getContext to return our own WebGLContext\n            // This will be called inside the mapboxgl.Map constructor\n            // @ts-expect-error\n            HTMLCanvasElement.prototype.getContext = ()=>{\n                // Unhijack immediately\n                HTMLCanvasElement.prototype.getContext = getContext;\n                return props.gl;\n            };\n        }\n        const map = new this._MapClass(mapOptions);\n        // Props that are not part of constructor options\n        if (viewState.padding) {\n            map.setPadding(viewState.padding);\n        }\n        if (props.cursor) {\n            map.getCanvas().style.cursor = props.cursor;\n        }\n        this._createShadowTransform(map);\n        // Hack\n        // Insert code into map's render cycle\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        const renderMap = map._render;\n        map._render = (arg)=>{\n            this._inRender = true;\n            renderMap.call(map, arg);\n            this._inRender = false;\n        };\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        const runRenderTaskQueue = map._renderTaskQueue.run;\n        map._renderTaskQueue.run = (arg)=>{\n            runRenderTaskQueue.call(map._renderTaskQueue, arg);\n            this._onBeforeRepaint();\n        };\n        map.on('render', ()=>this._onAfterRepaint());\n        // Insert code into map's event pipeline\n        // eslint-disable-next-line @typescript-eslint/unbound-method\n        const fireEvent = map.fire;\n        map.fire = this._fireEvent.bind(this, fireEvent);\n        // add listeners\n        map.on('resize', ()=>{\n            this._renderTransform.resize(map.transform.width, map.transform.height);\n        });\n        map.on('styledata', ()=>{\n            this._updateStyleComponents(this.props, {});\n            // Projection can be set in stylesheet\n            (0,_utils_transform_js__WEBPACK_IMPORTED_MODULE_0__.syncProjection)(map.transform, this._renderTransform);\n        });\n        map.on('sourcedata', ()=>this._updateStyleComponents(this.props, {}));\n        for(const eventName in pointerEvents){\n            map.on(eventName, this._onPointerEvent);\n        }\n        for(const eventName in cameraEvents){\n            map.on(eventName, this._onCameraEvent);\n        }\n        for(const eventName in otherEvents){\n            map.on(eventName, this._onEvent);\n        }\n        this._map = map;\n    }\n    /* eslint-enable complexity,max-statements */ recycle() {\n        // Clean up unnecessary elements before storing for reuse.\n        const container = this.map.getContainer();\n        const children = container.querySelector('[mapboxgl-children]');\n        children?.remove();\n        Mapbox.savedMaps.push(this);\n    }\n    destroy() {\n        this._map.remove();\n    }\n    // Force redraw the map now. Typically resize() and jumpTo() is reflected in the next\n    // render cycle, which is managed by Mapbox's animation loop.\n    // This removes the synchronization issue caused by requestAnimationFrame.\n    redraw() {\n        const map = this._map;\n        // map._render will throw error if style does not exist\n        // https://github.com/mapbox/mapbox-gl-js/blob/fb9fc316da14e99ff4368f3e4faa3888fb43c513\n        //   /src/ui/map.js#L1834\n        if (!this._inRender && map.style) {\n            // cancel the scheduled update\n            if (map._frame) {\n                map._frame.cancel();\n                map._frame = null;\n            }\n            // the order is important - render() may schedule another update\n            map._render();\n        }\n    }\n    _createShadowTransform(map) {\n        const renderTransform = (0,_utils_transform_js__WEBPACK_IMPORTED_MODULE_0__.cloneTransform)(map.transform);\n        map.painter.transform = renderTransform;\n        this._renderTransform = renderTransform;\n    }\n    /* Trigger map resize if size is controlled\n       @param {object} nextProps\n       @returns {bool} true if size has changed\n     */ _updateSize(nextProps) {\n        // Check if size is controlled\n        const { viewState } = nextProps;\n        if (viewState) {\n            const map = this._map;\n            if (viewState.width !== map.transform.width || viewState.height !== map.transform.height) {\n                map.resize();\n                return true;\n            }\n        }\n        return false;\n    }\n    // Adapted from map.jumpTo\n    /* Update camera to match props\n       @param {object} nextProps\n       @param {bool} triggerEvents - should fire camera events\n       @returns {bool} true if anything is changed\n     */ _updateViewState(nextProps, triggerEvents) {\n        if (this._internalUpdate) {\n            return false;\n        }\n        const map = this._map;\n        const tr = this._renderTransform;\n        // Take a snapshot of the transform before mutation\n        const { zoom, pitch, bearing } = tr;\n        const isMoving = map.isMoving();\n        if (isMoving) {\n            // All movement of the camera is done relative to the sea level\n            tr.cameraElevationReference = 'sea';\n        }\n        const changed = (0,_utils_transform_js__WEBPACK_IMPORTED_MODULE_0__.applyViewStateToTransform)(tr, {\n            ...(0,_utils_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformToViewState)(map.transform),\n            ...nextProps\n        });\n        if (isMoving) {\n            // Reset camera reference\n            tr.cameraElevationReference = 'ground';\n        }\n        if (changed && triggerEvents) {\n            const deferredEvents = this._deferredEvents;\n            // Delay DOM control updates to the next render cycle\n            deferredEvents.move = true;\n            deferredEvents.zoom || (deferredEvents.zoom = zoom !== tr.zoom);\n            deferredEvents.rotate || (deferredEvents.rotate = bearing !== tr.bearing);\n            deferredEvents.pitch || (deferredEvents.pitch = pitch !== tr.pitch);\n        }\n        // Avoid manipulating the real transform when interaction/animation is ongoing\n        // as it would interfere with Mapbox's handlers\n        if (!isMoving) {\n            (0,_utils_transform_js__WEBPACK_IMPORTED_MODULE_0__.applyViewStateToTransform)(map.transform, nextProps);\n        }\n        return changed;\n    }\n    /* Update camera constraints and projection settings to match props\n       @param {object} nextProps\n       @param {object} currProps\n       @returns {bool} true if anything is changed\n     */ _updateSettings(nextProps, currProps) {\n        const map = this._map;\n        let changed = false;\n        for (const propName of settingNames){\n            if (propName in nextProps && !(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_2__.deepEqual)(nextProps[propName], currProps[propName])) {\n                changed = true;\n                const setter = map[`set${propName[0].toUpperCase()}${propName.slice(1)}`];\n                setter?.call(map, nextProps[propName]);\n            }\n        }\n        return changed;\n    }\n    /* Update map style to match props\n       @param {object} nextProps\n       @param {object} currProps\n       @returns {bool} true if style is changed\n     */ _updateStyle(nextProps, currProps) {\n        if (nextProps.cursor !== currProps.cursor) {\n            this._map.getCanvas().style.cursor = nextProps.cursor || '';\n        }\n        if (nextProps.mapStyle !== currProps.mapStyle) {\n            const { mapStyle = DEFAULT_STYLE, styleDiffing = true } = nextProps;\n            const options = {\n                diff: styleDiffing\n            };\n            if ('localIdeographFontFamily' in nextProps) {\n                // @ts-ignore Mapbox specific prop\n                options.localIdeographFontFamily = nextProps.localIdeographFontFamily;\n            }\n            this._map.setStyle((0,_utils_style_utils_js__WEBPACK_IMPORTED_MODULE_1__.normalizeStyle)(mapStyle), options);\n            return true;\n        }\n        return false;\n    }\n    /* Update fog, light and terrain to match props\n       @param {object} nextProps\n       @param {object} currProps\n       @returns {bool} true if anything is changed\n     */ _updateStyleComponents(nextProps, currProps) {\n        const map = this._map;\n        let changed = false;\n        if (map.isStyleLoaded()) {\n            if ('light' in nextProps && map.setLight && !(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_2__.deepEqual)(nextProps.light, currProps.light)) {\n                changed = true;\n                map.setLight(nextProps.light);\n            }\n            if ('fog' in nextProps && map.setFog && !(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_2__.deepEqual)(nextProps.fog, currProps.fog)) {\n                changed = true;\n                map.setFog(nextProps.fog);\n            }\n            if ('terrain' in nextProps && map.setTerrain && !(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_2__.deepEqual)(nextProps.terrain, currProps.terrain)) {\n                if (!nextProps.terrain || map.getSource(nextProps.terrain.source)) {\n                    changed = true;\n                    map.setTerrain(nextProps.terrain);\n                }\n            }\n        }\n        return changed;\n    }\n    /* Update interaction handlers to match props\n       @param {object} nextProps\n       @param {object} currProps\n       @returns {bool} true if anything is changed\n     */ _updateHandlers(nextProps, currProps) {\n        const map = this._map;\n        let changed = false;\n        for (const propName of handlerNames){\n            const newValue = nextProps[propName] ?? true;\n            const oldValue = currProps[propName] ?? true;\n            if (!(0,_utils_deep_equal_js__WEBPACK_IMPORTED_MODULE_2__.deepEqual)(newValue, oldValue)) {\n                changed = true;\n                if (newValue) {\n                    map[propName].enable(newValue);\n                } else {\n                    map[propName].disable();\n                }\n            }\n        }\n        return changed;\n    }\n    _queryRenderedFeatures(point) {\n        const map = this._map;\n        const tr = map.transform;\n        const { interactiveLayerIds = [] } = this.props;\n        try {\n            map.transform = this._renderTransform;\n            return map.queryRenderedFeatures(point, {\n                layers: interactiveLayerIds.filter(map.getLayer.bind(map))\n            });\n        } catch  {\n            // May fail if style is not loaded\n            return [];\n        } finally{\n            map.transform = tr;\n        }\n    }\n    _updateHover(e) {\n        const { props } = this;\n        const shouldTrackHoveredFeatures = props.interactiveLayerIds && (props.onMouseMove || props.onMouseEnter || props.onMouseLeave);\n        if (shouldTrackHoveredFeatures) {\n            const eventType = e.type;\n            const wasHovering = this._hoveredFeatures?.length > 0;\n            const features = this._queryRenderedFeatures(e.point);\n            const isHovering = features.length > 0;\n            if (!isHovering && wasHovering) {\n                e.type = 'mouseleave';\n                this._onPointerEvent(e);\n            }\n            this._hoveredFeatures = features;\n            if (isHovering && !wasHovering) {\n                e.type = 'mouseenter';\n                this._onPointerEvent(e);\n            }\n            e.type = eventType;\n        } else {\n            this._hoveredFeatures = null;\n        }\n    }\n    _fireEvent(baseFire, event, properties) {\n        const map = this._map;\n        const tr = map.transform;\n        const eventType = typeof event === 'string' ? event : event.type;\n        if (eventType === 'move') {\n            this._updateViewState(this.props, false);\n        }\n        if (eventType in cameraEvents) {\n            if (typeof event === 'object') {\n                event.viewState = (0,_utils_transform_js__WEBPACK_IMPORTED_MODULE_0__.transformToViewState)(tr);\n            }\n            if (this._map.isMoving()) {\n                // Replace map.transform with ours during the callbacks\n                map.transform = this._renderTransform;\n                baseFire.call(map, event, properties);\n                map.transform = tr;\n                return map;\n            }\n        }\n        baseFire.call(map, event, properties);\n        return map;\n    }\n    // All camera manipulations are complete, ready to repaint\n    _onBeforeRepaint() {\n        const map = this._map;\n        // If there are camera changes driven by props, invoke camera events so that DOM controls are synced\n        this._internalUpdate = true;\n        for(const eventType in this._deferredEvents){\n            if (this._deferredEvents[eventType]) {\n                map.fire(eventType);\n            }\n        }\n        this._internalUpdate = false;\n        const tr = this._map.transform;\n        // Make sure camera matches the current props\n        map.transform = this._renderTransform;\n        this._onAfterRepaint = ()=>{\n            // Mapbox transitions between non-mercator projection and mercator during render time\n            // Copy it back to the other\n            (0,_utils_transform_js__WEBPACK_IMPORTED_MODULE_0__.syncProjection)(this._renderTransform, tr);\n            // Restores camera state before render/load events are fired\n            map.transform = tr;\n        };\n    }\n}\nMapbox.savedMaps = [];\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Mapbox);\n/**\n * Access token can be provided via one of:\n *   mapboxAccessToken prop\n *   access_token query parameter\n *   MapboxAccessToken environment variable\n *   REACT_APP_MAPBOX_ACCESS_TOKEN environment variable\n * @returns access token\n */ function getAccessTokenFromEnv() {\n    let accessToken = null;\n    /* global location, process */ if (typeof location !== 'undefined') {\n        const match = /access_token=([^&\\/]*)/.exec(location.search);\n        accessToken = match && match[1];\n    }\n    // Note: This depends on bundler plugins (e.g. webpack) importing environment correctly\n    try {\n        // eslint-disable-next-line no-process-env\n        accessToken = accessToken || process.env.MapboxAccessToken;\n    } catch  {\n    // ignore\n    }\n    try {\n        // eslint-disable-next-line no-process-env\n        accessToken = accessToken || process.env.REACT_APP_MAPBOX_ACCESS_TOKEN;\n    } catch  {\n    // ignore\n    }\n    return accessToken;\n} //# sourceMappingURL=mapbox.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/mapbox/mapbox.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/types/common.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/types/common.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
 //# sourceMappingURL=common.js.map


/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/types/events.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/types/events.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
 //# sourceMappingURL=events.js.map


/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/types/lib.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/types/lib.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
 //# sourceMappingURL=lib.js.map


/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/types/style-spec.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/types/style-spec.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
 //# sourceMappingURL=style-spec.js.map


/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/apply-react-style.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/utils/apply-react-style.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyReactStyle: () => (/* binding */ applyReactStyle)\n/* harmony export */ });\n// This is a simplified version of\n// https://github.com/facebook/react/blob/4131af3e4bf52f3a003537ec95a1655147c81270/src/renderers/dom/shared/CSSPropertyOperations.js#L62\nconst unitlessNumber = /box|flex|grid|column|lineHeight|fontWeight|opacity|order|tabSize|zIndex/;\nfunction applyReactStyle(element, styles) {\n    if (!element || !styles) {\n        return;\n    }\n    const style = element.style;\n    for(const key in styles){\n        const value = styles[key];\n        if (Number.isFinite(value) && !unitlessNumber.test(key)) {\n            style[key] = `${value}px`;\n        } else {\n            style[key] = value;\n        }\n    }\n} //# sourceMappingURL=apply-react-style.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS91dGlscy9hcHBseS1yZWFjdC1zdHlsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQ0Esa0NBQWtDO0FBQ2xDLHdJQUF3STtBQUN4SSxNQUFNLGNBQWMsR0FBRyx5RUFBeUUsQ0FBQztBQUUzRixTQUFVLGVBQWUsQ0FBQyxPQUFvQixFQUFFLE1BQTJCO0lBQy9FLElBQUksQ0FBQyxPQUFPLElBQUksQ0FBQyxNQUFNLEVBQUUsQ0FBQztRQUN4QixPQUFPO0lBQ1QsQ0FBQztJQUNELE1BQU0sS0FBSyxHQUFHLE9BQU8sQ0FBQyxLQUFLLENBQUM7SUFFNUIsSUFBSyxNQUFNLEdBQUcsSUFBSSxNQUFNLENBQUUsQ0FBQztRQUN6QixNQUFNLEtBQUssR0FBRyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDMUIsSUFBSSxNQUFNLENBQUMsUUFBUSxDQUFDLEtBQUssQ0FBQyxJQUFJLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsRUFBRSxDQUFDO1lBQ3hELEtBQUssQ0FBQyxHQUFHLENBQUMsR0FBRyxHQUFHLEtBQUssSUFBSSxDQUFDO1FBQzVCLENBQUMsTUFBTSxDQUFDO1lBQ04sS0FBSyxDQUFDLEdBQUcsQ0FBQyxHQUFHLEtBQUssQ0FBQztRQUNyQixDQUFDO0lBQ0gsQ0FBQztBQUNILENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxzcmNcXG1hcGJveC1sZWdhY3lcXHV0aWxzXFxhcHBseS1yZWFjdC1zdHlsZS50cyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/apply-react-style.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/assert.js":
/*!**********************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/utils/assert.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ assert)\n/* harmony export */ });\nfunction assert(condition, message) {\n    if (!condition) {\n        throw new Error(message);\n    }\n} //# sourceMappingURL=assert.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS91dGlscy9hc3NlcnQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFjLFNBQVUsTUFBTSxDQUFDLFNBQWMsRUFBRSxPQUFlO0lBQzVELElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQztRQUNmLE1BQU0sSUFBSSxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUM7SUFDM0IsQ0FBQztBQUNILENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxzcmNcXG1hcGJveC1sZWdhY3lcXHV0aWxzXFxhc3NlcnQudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/assert.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/deep-equal.js":
/*!**************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/utils/deep-equal.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arePointsEqual: () => (/* binding */ arePointsEqual),\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual)\n/* harmony export */ });\n/**\n * Compare two points\n * @param a\n * @param b\n * @returns true if the points are equal\n */ function arePointsEqual(a, b) {\n    const ax = Array.isArray(a) ? a[0] : a ? a.x : 0;\n    const ay = Array.isArray(a) ? a[1] : a ? a.y : 0;\n    const bx = Array.isArray(b) ? b[0] : b ? b.x : 0;\n    const by = Array.isArray(b) ? b[1] : b ? b.y : 0;\n    return ax === bx && ay === by;\n}\n/* eslint-disable complexity */ /**\n * Compare any two objects\n * @param a\n * @param b\n * @returns true if the objects are deep equal\n */ function deepEqual(a, b) {\n    if (a === b) {\n        return true;\n    }\n    if (!a || !b) {\n        return false;\n    }\n    if (Array.isArray(a)) {\n        if (!Array.isArray(b) || a.length !== b.length) {\n            return false;\n        }\n        for(let i = 0; i < a.length; i++){\n            if (!deepEqual(a[i], b[i])) {\n                return false;\n            }\n        }\n        return true;\n    } else if (Array.isArray(b)) {\n        return false;\n    }\n    if (typeof a === 'object' && typeof b === 'object') {\n        const aKeys = Object.keys(a);\n        const bKeys = Object.keys(b);\n        if (aKeys.length !== bKeys.length) {\n            return false;\n        }\n        for (const key of aKeys){\n            if (!b.hasOwnProperty(key)) {\n                return false;\n            }\n            if (!deepEqual(a[key], b[key])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    return false;\n} //# sourceMappingURL=deep-equal.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/deep-equal.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/set-globals.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/utils/set-globals.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ setGlobals)\n/* harmony export */ });\nconst globalSettings = [\n    'baseApiUrl',\n    'maxParallelImageRequests',\n    'workerClass',\n    'workerCount',\n    'workerUrl'\n];\nfunction setGlobals(mapLib, props) {\n    for (const key of globalSettings){\n        if (key in props) {\n            mapLib[key] = props[key];\n        }\n    }\n    const { RTLTextPlugin = 'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-rtl-text/v0.2.3/mapbox-gl-rtl-text.js' } = props;\n    if (RTLTextPlugin && mapLib.getRTLTextPluginStatus && mapLib.getRTLTextPluginStatus() === 'unavailable') {\n        mapLib.setRTLTextPlugin(RTLTextPlugin, (error)=>{\n            if (error) {\n                // eslint-disable-next-line\n                console.error(error);\n            }\n        }, true);\n    }\n} //# sourceMappingURL=set-globals.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS91dGlscy9zZXQtZ2xvYmFscy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBc0JBLE1BQU0sY0FBYyxHQUFHO0lBQ3JCLFlBQVk7SUFDWiwwQkFBMEI7SUFDMUIsYUFBYTtJQUNiLGFBQWE7SUFDYixXQUFXO0NBQ0gsQ0FBQztBQUVHLFNBQVUsVUFBVSxDQUFDLE1BQVcsRUFBRSxLQUFxQjtJQUNuRSxLQUFLLE1BQU0sR0FBRyxJQUFJLGNBQWMsQ0FBRSxDQUFDO1FBQ2pDLElBQUksR0FBRyxJQUFJLEtBQUssRUFBRSxDQUFDO1lBQ2pCLE1BQU0sQ0FBQyxHQUFHLENBQUMsR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDLENBQUM7UUFDM0IsQ0FBQztJQUNILENBQUM7SUFFRCxNQUFNLEVBQ0osYUFBYSxHQUFHLDZGQUE2RixFQUM5RyxHQUFHLEtBQUssQ0FBQztJQUNWLElBQ0UsYUFBYSxJQUNiLE1BQU0sQ0FBQyxzQkFBc0IsSUFDN0IsTUFBTSxDQUFDLHNCQUFzQixFQUFFLEtBQUssYUFBYSxFQUNqRCxDQUFDO1FBQ0QsTUFBTSxDQUFDLGdCQUFnQixDQUNyQixhQUFhLEVBQ2IsQ0FBQyxLQUFhLEVBQUUsRUFBRTtZQUNoQixJQUFJLEtBQUssRUFBRSxDQUFDO2dCQUNWLDJCQUEyQjtnQkFDM0IsT0FBTyxDQUFDLEtBQUssQ0FBQyxLQUFLLENBQUMsQ0FBQztZQUN2QixDQUFDO1FBQ0gsQ0FBQyxFQUNELElBQUksQ0FDTCxDQUFDO0lBQ0osQ0FBQztBQUNILENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRG9jdW1lbnRzXFxzcmNcXG1hcGJveC1sZWdhY3lcXHV0aWxzXFxzZXQtZ2xvYmFscy50cyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/set-globals.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/style-utils.js":
/*!***************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/utils/style-utils.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalizeStyle: () => (/* binding */ normalizeStyle)\n/* harmony export */ });\nconst refProps = [\n    'type',\n    'source',\n    'source-layer',\n    'minzoom',\n    'maxzoom',\n    'filter',\n    'layout'\n];\n// Prepare a map style object for diffing\n// If immutable - convert to plain object\n// Work around some issues in older styles that would fail Mapbox's diffing\nfunction normalizeStyle(style) {\n    if (!style) {\n        return null;\n    }\n    if (typeof style === 'string') {\n        return style;\n    }\n    if ('toJS' in style) {\n        style = style.toJS();\n    }\n    if (!style.layers) {\n        return style;\n    }\n    const layerIndex = {};\n    for (const layer of style.layers){\n        layerIndex[layer.id] = layer;\n    }\n    const layers = style.layers.map((layer)=>{\n        let normalizedLayer = null;\n        if ('interactive' in layer) {\n            normalizedLayer = Object.assign({}, layer);\n            // Breaks style diffing :(\n            // @ts-ignore legacy field not typed\n            delete normalizedLayer.interactive;\n        }\n        // Style diffing doesn't work with refs so expand them out manually before diffing.\n        // @ts-ignore legacy field not typed\n        const layerRef = layerIndex[layer.ref];\n        if (layerRef) {\n            normalizedLayer = normalizedLayer || Object.assign({}, layer);\n            // @ts-ignore\n            delete normalizedLayer.ref;\n            // https://github.com/mapbox/mapbox-gl-js/blob/master/src/style-spec/deref.js\n            for (const propName of refProps){\n                if (propName in layerRef) {\n                    normalizedLayer[propName] = layerRef[propName];\n                }\n            }\n        }\n        return normalizedLayer || layer;\n    });\n    // Do not mutate the style object provided by the user\n    return {\n        ...style,\n        layers\n    };\n} //# sourceMappingURL=style-utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/style-utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/transform.js":
/*!*************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/utils/transform.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applyViewStateToTransform: () => (/* binding */ applyViewStateToTransform),\n/* harmony export */   cloneTransform: () => (/* binding */ cloneTransform),\n/* harmony export */   syncProjection: () => (/* binding */ syncProjection),\n/* harmony export */   transformToViewState: () => (/* binding */ transformToViewState)\n/* harmony export */ });\n/* harmony import */ var _deep_equal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deep-equal.js */ \"(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/deep-equal.js\");\n\n/**\n * Make a copy of a transform\n * @param tr\n */ function cloneTransform(tr) {\n    const newTransform = tr.clone();\n    // Work around mapbox bug - this value is not assigned in clone(), only in resize()\n    newTransform.pixelsToGLUnits = tr.pixelsToGLUnits;\n    return newTransform;\n}\n/**\n * Copy projection from one transform to another. This only applies to mapbox-gl transforms\n * @param src the transform to copy projection settings from\n * @param dest to transform to copy projection settings to\n */ function syncProjection(src, dest) {\n    if (!src.getProjection) {\n        return;\n    }\n    const srcProjection = src.getProjection();\n    const destProjection = dest.getProjection();\n    if (!(0,_deep_equal_js__WEBPACK_IMPORTED_MODULE_0__.deepEqual)(srcProjection, destProjection)) {\n        dest.setProjection(srcProjection);\n    }\n}\n/**\n * Capture a transform's current state\n * @param transform\n * @returns descriptor of the view state\n */ function transformToViewState(tr) {\n    return {\n        longitude: tr.center.lng,\n        latitude: tr.center.lat,\n        zoom: tr.zoom,\n        pitch: tr.pitch,\n        bearing: tr.bearing,\n        padding: tr.padding\n    };\n}\n/* eslint-disable complexity */ /**\n * Mutate a transform to match the given view state\n * @param transform\n * @param viewState\n * @returns true if the transform has changed\n */ function applyViewStateToTransform(tr, props) {\n    const v = props.viewState || props;\n    let changed = false;\n    if ('zoom' in v) {\n        const zoom = tr.zoom;\n        tr.zoom = v.zoom;\n        changed = changed || zoom !== tr.zoom;\n    }\n    if ('bearing' in v) {\n        const bearing = tr.bearing;\n        tr.bearing = v.bearing;\n        changed = changed || bearing !== tr.bearing;\n    }\n    if ('pitch' in v) {\n        const pitch = tr.pitch;\n        tr.pitch = v.pitch;\n        changed = changed || pitch !== tr.pitch;\n    }\n    if (v.padding && !tr.isPaddingEqual(v.padding)) {\n        changed = true;\n        tr.padding = v.padding;\n    }\n    if ('longitude' in v && 'latitude' in v) {\n        const center = tr.center;\n        // @ts-ignore\n        tr.center = new center.constructor(v.longitude, v.latitude);\n        changed = changed || center !== tr.center;\n    }\n    return changed;\n} //# sourceMappingURL=transform.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/use-isomorphic-layout-effect.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/react-map-gl/dist/mapbox-legacy/utils/use-isomorphic-layout-effect.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// From https://github.com/streamich/react-use/blob/master/src/useIsomorphicLayoutEffect.ts\n// useLayoutEffect but does not trigger warning in server-side rendering\n\nconst useIsomorphicLayoutEffect = typeof document !== 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useIsomorphicLayoutEffect); //# sourceMappingURL=use-isomorphic-layout-effect.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtbWFwLWdsL2Rpc3QvbWFwYm94LWxlZ2FjeS91dGlscy91c2UtaXNvbW9ycGhpYy1sYXlvdXQtZWZmZWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsMkZBQTJGO0FBQzNGLHdFQUF3RTtBQUN2QjtBQUVqRCxNQUFNLHlCQUF5QixHQUFHLE9BQU8sUUFBUSxLQUFLLFdBQVcsQ0FBQyxDQUFDLENBQUMsa0RBQWUsQ0FBQyxDQUFDLENBQUMsNENBQVMsQ0FBQztBQUVoRyxpRUFBZSx5QkFBeUIsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxVU0VSXFxEb2N1bWVudHNcXHNyY1xcbWFwYm94LWxlZ2FjeVxcdXRpbHNcXHVzZS1pc29tb3JwaGljLWxheW91dC1lZmZlY3QudHMiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-map-gl/dist/mapbox-legacy/utils/use-isomorphic-layout-effect.js\n");

/***/ })

};
;