import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/auth";
import {
  createPropertyComment,
  deletePropertyComment,
  getPropertyComments,
  updatePropertyComment,
} from "@/lib/db";

// Schema for comment creation/update
const commentSchema = z.object({
  content: z.string().min(1).max(1000),
});

// GET /api/properties/[id]/comments
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;
    const comments = await getPropertyComments(propertyId);

    return NextResponse.json(comments);
  } catch (error) {
    console.error("Error fetching property comments:", error);
    return NextResponse.json(
      { error: "Failed to fetch property comments" },
      { status: 500 }
    );
  }
}

// POST /api/properties/[id]/comments
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "You must be logged in to comment on a property" },
        { status: 401 }
      );
    }

    const propertyId = params.id;
    const userId = session.user.id;
    const body = await request.json();

    // Validate request body
    const validatedData = commentSchema.parse(body);

    // Create comment
    const comment = await createPropertyComment({
      content: validatedData.content,
      userId,
      propertyId,
    });

    if (!comment) {
      return NextResponse.json(
        { error: "Failed to create comment" },
        { status: 500 }
      );
    }

    return NextResponse.json(comment);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }

    console.error("Error creating property comment:", error);
    return NextResponse.json(
      { error: "Failed to create property comment" },
      { status: 500 }
    );
  }
}

// PATCH /api/properties/[id]/comments?commentId=xxx
export async function PATCH(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "You must be logged in to update a comment" },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const commentId = url.searchParams.get("commentId");

    if (!commentId) {
      return NextResponse.json(
        { error: "Comment ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Validate request body
    const validatedData = commentSchema.parse(body);

    // Update comment
    const comment = await updatePropertyComment(commentId, {
      content: validatedData.content,
    });

    if (!comment) {
      return NextResponse.json(
        { error: "Failed to update comment" },
        { status: 500 }
      );
    }

    return NextResponse.json(comment);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }

    console.error("Error updating property comment:", error);
    return NextResponse.json(
      { error: "Failed to update property comment" },
      { status: 500 }
    );
  }
}

// DELETE /api/properties/[id]/comments?commentId=xxx
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "You must be logged in to delete a comment" },
        { status: 401 }
      );
    }

    const url = new URL(request.url);
    const commentId = url.searchParams.get("commentId");

    if (!commentId) {
      return NextResponse.json(
        { error: "Comment ID is required" },
        { status: 400 }
      );
    }

    // Delete the comment
    await deletePropertyComment(commentId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting property comment:", error);
    return NextResponse.json(
      { error: "Failed to delete property comment" },
      { status: 500 }
    );
  }
}
