import { NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/auth";
import {
  createPropertyRating,
  deletePropertyRating,
  getPropertyRatings,
  getUserPropertyRating,
} from "@/lib/db";

// Schema for rating creation/update
const ratingSchema = z.object({
  rating: z.number().min(1).max(5),
});

// GET /api/properties/[id]/ratings
export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const propertyId = params.id;
    const ratings = await getPropertyRatings(propertyId);

    return NextResponse.json(ratings);
  } catch (error) {
    console.error("Error fetching property ratings:", error);
    return NextResponse.json(
      { error: "Failed to fetch property ratings" },
      { status: 500 }
    );
  }
}

// POST /api/properties/[id]/ratings
export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "You must be logged in to rate a property" },
        { status: 401 }
      );
    }

    const propertyId = params.id;
    const userId = session.user.id;
    const body = await request.json();

    // Validate request body
    const validatedData = ratingSchema.parse(body);

    // Create or update rating
    const rating = await createPropertyRating({
      rating: validatedData.rating,
      userId,
      propertyId,
    });

    if (!rating) {
      return NextResponse.json(
        { error: "Failed to create rating" },
        { status: 500 }
      );
    }

    return NextResponse.json(rating);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: error.errors }, { status: 400 });
    }

    console.error("Error creating property rating:", error);
    return NextResponse.json(
      { error: "Failed to create property rating" },
      { status: 500 }
    );
  }
}

// DELETE /api/properties/[id]/ratings
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "You must be logged in to delete a rating" },
        { status: 401 }
      );
    }

    const propertyId = params.id;
    const userId = session.user.id;

    // Get user's rating for this property
    const rating = await getUserPropertyRating(userId, propertyId);

    if (!rating) {
      return NextResponse.json({ error: "Rating not found" }, { status: 404 });
    }

    // Delete the rating
    await deletePropertyRating(rating.id);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting property rating:", error);
    return NextResponse.json(
      { error: "Failed to delete property rating" },
      { status: 500 }
    );
  }
}
